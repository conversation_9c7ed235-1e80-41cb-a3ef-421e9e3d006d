## 1.0.5（2023-05-26）
处理初始化html，异步获取到html时已经初始化过了，导致未设置进编辑器的问题
## 1.0.4（2023-05-06）
有伙伴反应市场上的示例项目运行不起来，我重新上传一次
## 1.0.3（2023-04-23）
因app端插入emoji会强制弹出键盘，取消插入emoji不弹出键盘的功能，tab栏增加收起，另外还有一个问题，如果从中间部分删除链接会导致光标指向位置不正确的问题暂未解决，我再想想办法
## 1.0.2（2023-04-23）
因app端插入emoji会强制弹出键盘，取消插入emoji不弹出键盘的功能，tab栏增加收起，另外还有一个问题，如果从中间部分删除链接会导致光标指向位置不正确的问题暂未解决，我再想想办法
## 1.0.1（2023-04-21）
刚发布出来市场里就跑第五页去了?，重新发布一下
## 1.0.0（2023-04-21）
有问题可以随时评论或Q群
