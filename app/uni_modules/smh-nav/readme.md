# smh-nav
# ### 安装方式

本组件符合[easycom](https://uniapp.dcloud.io/collocation/pages?id=easycom)规范，`HBuilderX 2.5.5`起，只需将本组件导入项目，在页面`template`中即可直接使用，无需在页面中`import`和注册`components`

### 基本用法

在 ``template`` 中的使用
```html
<!-- 基本用法 -->
<smh-nav :list="list" :current="defaultindex" @change="switchs"></smh-nav>
data() {
	return {
		list:[
			{name:"个人中心",id:1},
			{name:"关于我们",id:2},
			{name:"新闻中心",id:3},
			{name:"联系我们",id:4}
		],
		defaultindex:1 //默认让下标为1的数据选中，下标从0开始
	}
}
<!-- 基本用法 -->
@change会在选项改变时触发，会返回选项的下标和id，各位可以根据返回的下标或者id来进行对应的操作。
methods: {
	switchs(e){
		let index= e.index //下标
		this.defaultindex=index
		let id = e.id //id
	}
}