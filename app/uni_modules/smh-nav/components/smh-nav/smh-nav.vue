<template>
	<view class="nav">
		<view class="dz" :class="{active:defaultIndex==index}" @click="changeClass(index,item.id)" v-for="(item,index) in list" :key="index">
			{{item.name}}
		</view>
	</view>
</template>

<script>
	export default {
		name: 'navs',
		props:{
			list:{
				type: Array,
				default() {
					return [
						{name:"选项一",id:1},
						{name:"选项二",id:2},
						{name:"选项三",id:3}
					];
				}
			},
			current: {
				type: Number,
				default: 0
			}
		},
		watch:{
			current:function(val){
				this.defaultIndex=val
			}
		},
		data() {
			return {
				defaultIndex:0,
				widths:"0"
			}
		},
		created(){
			this.defaultIndex=this.current
		},
		computed:{
		},
		methods: {
			changeClass:function(i,id){
				this.defaultIndex=i
				this.$emit('change',{index:i,id:id})
			}
		}
	}
</script>

<style scoped lang="scss">
.nav{
	width: 100%;
	height: 80rpx;
	margin: 0 auto;
	display: flex;
	justify-content:space-around;
	&>.dz{
		text-align:center;
		color: #999999;
		position: relative;
		line-height: 80rpx;
		font-size: 30rpx;
	}
	.active{
		color: #333333;
		&::after{
			content: "";
			width: 70rpx;
			height: 6rpx;
			position: absolute;
			background-color: #13227a;
			top: 92%;
			left: 50%;
			transform: translateX(-50%);
		}
	}
	.active1{
		color: #FF6873;
		&::after{
			content: "";
			width: 60rpx;
			height: 4rpx;
			position: absolute;
			background-color: #13227a;
			top: 100%;
			left: 50%;
			transform: translateX(-50%);
		}
	}
	
}
</style>
