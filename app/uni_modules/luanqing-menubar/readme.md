# luanqing-menubar
本插件符合最新规范，直接导入项目即可使用

##示例：
```
<template>
	<view>
		<luanqing-menubar :menus="menus" @clickMenu="clickMenu" :fixed="true"></luanqing-menubar>
	</view>
</template>

数据格式：(属性中必须带有一个name属性用于显示，其他数据自行设定，点击后会调用clickMenu回调返回选中的item object数据)
menus:[{id:1,name:'帮派群聊'},{id:2,name:'帮派信息'},{id:6,name:'帮派仓库'},{id:3,name:'成员列表'},{id:4,name:'申请列表'},{id:5,name:'其他帮派'}],

```

##参数
名称|作用|说明
--|:--:|--:
menus|菜单数据|[{name:xxx}]必须持有至少一个name属性
fixed|是否固定菜单栏|默认固定[ true/false]
`clickMenu`|选中回调|`clickMenu`带回一个当前选择的菜单数据item



##说明：
1.  menus  菜单栏数据  [{name:"xx"}]  至少需要一个name属性
2.  clickMenu  组件内部点击时会回调该函数，开发者可以在此接收选中的菜单(回传参数: menu对象)


##具体详细参数如下：
```
	props: {
			// 设置是否固定菜单栏
			fixed:{
				type:Boolean,
				default:true,
			},
			// 菜单数据
			menus: {
				type: Array,
				value: []
			},
			// 当前选中菜单项目(不传则默认第一项)
			defaultSelect:{
				type:Object,
				value:{},
			},
			// 文字激活时的风格
			activeTextStyle:{
				type: String,
				default:"menu_item_text_active",
			},
			// 下横线激活时的风格
			activeLineStyle:{
				type: String,
				default:"menu_item_line_active",
			},
			// 文字普通风格
			textStyle:{
				type: String,
				default: "menu_item_text"
			},
			// 下横线普通风格
			lineStyle:{
				type: String,
				default: "menu_item_line"
			}
		},
```