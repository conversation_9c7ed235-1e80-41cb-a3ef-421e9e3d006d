<template>
	<view :class="fixed ? 'menu_bar_view':''">
		<view :class="fixed ? 'fixed_menu_bar':'layout'">
			<view v-for="(menu,index) in menus" v-bind:key="index" class="menu_item" @click="doClickItem(menu)">
				<view :class="current.name === menu.name ? activeTextStyle:textStyle">{{menu.name}}</view>
				<!-- <view :class="current.name === menu.name ? activeLineStyle:lineStyle"></view> -->
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				current: {},
			}
		},
		created() {
			// 初始化  默认第一个是选中
			if (JSON.stringify(this.$props.defaultSelect) === '{}' || (this.$props.defaultSelect) === undefined) {
				this.current = this.$props.menus ? this.$props.menus[0] : {};
			} else {
				this.current = this.$props.defaultSelect;
			}
		},
		//props接收父组件传递得数据
		props: {
			// 设置是否固定菜单栏
			fixed: {
				type: Boolean,
				default: true,
			},
			// 菜单数据
			menus: {
				type: Array,
				value: []
			},
			// 当前选中菜单项目(不传则默认第一项)
			defaultSelect: {
				type: Object,
				value: {},
			},
			// 文字激活时的风格
			activeTextStyle: {
				type: String,
				default: "menu_item_text_active",
			},
			// 下横线激活时的风格
			activeLineStyle: {
				type: String,
				default: "menu_item_line_active",
			},
			// 文字普通风格
			textStyle: {
				type: String,
				default: "menu_item_text"
			},
			// 下横线普通风格
			lineStyle: {
				type: String,
				default: "menu_item_line"
			}
		},
		methods: {
			doClickItem(menu) {
				this.current = menu;
				this.$emit('clickMenu', menu);
			}
		}
	}
</script>

<style>
	@import url("xcss.css");

	.flex_row {
		display: flex;
		align-items: center;
		justify-content: flex-start;
	}

	.menu_item {
		width: 25%;
		text-align: center;
	}

	/* 固定顶部菜单栏 START */
	.menu_bar_view {
		margin-top: 90rpx;
	}

	.fixed_menu_bar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		padding: 10px 0;
		display: flex;
		padding-top: 20rpx;
		width: calc(100vw);
		align-items: flex-end;
		white-space: nowrap;
		flex-shrink: 0;
		justify-content: flex-start;
		overflow-x: auto;
		/*IE下隐藏滚动条*/
		-ms-overflow-style: none;
		/*火狐下隐藏滚动条*/
		overflow: -moz-scrollbars-none;
		scrollbar-width: none;
	}

	/* 固定顶部菜单栏 END */

	.menu_item_text_active {
		font-size: 28upx;
		font-weight: bold;
		color: #FF2400;
		background-color: #ffffff;
		padding: 5px 8px;
		border-radius: 50px;
	}

	.menu_item_text {
		font-size: 28upx;
		padding: 5px 8px;
		color: #ffffff;
	}

	.menu_item_line_active {
		margin-top: 6rpx;
		min-width: 65rpx;
		border-radius: 50rpx;
		height: 5rpx;
		background-color: #FF2400;
	}

	.menu_item_line {
		margin-top: 6rpx;
		min-width: 65rpx;
		height: 5rpx;
	}

	.layout {
		display: flex;
		width: 75vw;
		white-space: nowrap;
		flex-shrink: 0;
		-ms-overflow-style: none;
		overflow: -moz-scrollbars-none;
		scrollbar-width: none;
		background-color: #db5305;
		padding: 2px;
		margin: auto;
		border-radius: 50px;
	}

	/* 隐藏滚动条 */
	.layout::-webkit-scrollbar {
		display: none;
	}
</style>
