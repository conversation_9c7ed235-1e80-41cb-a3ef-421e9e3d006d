{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__6C7267F", "name": "铂时", "version": {"name": "1.1.6", "code": 116}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Camera": {}, "VideoPlayer": {}, "Maps": {"coordType": "gcj02"}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"autoclose": false, "waiting": false, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": false, "style": "dark", "background": "#F8F8F8"}, "compatible": {"ignoreVersion": true}, "usingComponents": true, "distribute": {"splashscreen": {"androidStyle": "common", "android": {"xxhdpi": "static/bg/kai.png", "xhdpi": "static/bg/kai.png", "hdpi": "static/bg/kai.png"}, "iosStyle": "common", "ios": {"iphone": {}}, "useOriginalMsgbox": true}, "icons": {"android": {}, "ios": {"ipad": {}, "iphone": {}}}, "google": {"permissions": ["<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "apple": {"dSYMs": false}, "plugins": {"ad": {}, "oauth": {}, "maps": {"amap": {"appkey_ios": "9f9b5a2736f6bca094142354d86ed36d", "appkey_android": "9f9b5a2736f6bca094142354d86ed36d"}}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#F8F8F8", "bottom": {"offset": "auto"}}, "uni-app": {"compilerVersion": "4.75", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"color": "#CFCFCF", "selectedColor": "#8147FF", "borderStyle": "rgba(0,0,0,0.4)", "backgroundColor": "#F8F8F8", "midButton": {"iconPath": "static/tabBar/<EMAIL>", "width": "70px", "height": "50px", "iconWidth": "50px"}, "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "static/img/tabbar/home.png", "selectedIconPath": "static/img/tabbar/homeactive.png"}, {"pagePath": "pages/talentList/talentList", "text": "论坛", "iconPath": "static/img/tabbar/guanzhu.png", "selectedIconPath": "static/img/tabbar/guanzhuactive.png"}, {"pagePath": "pages/mall/mall", "text": "商城", "iconPath": "static/img/tabbar/add.png", "selectedIconPath": "static/img/tabbar/addactive.png"}, {"pagePath": "pages/mine/mine", "text": "我的", "iconPath": "static/img/tabbar/me.png", "selectedIconPath": "static/img/tabbar/meactive.png"}], "height": "50px", "child": ["lauchwebview"], "selected": 0}, "launch_path": "__uniappview.html"}}