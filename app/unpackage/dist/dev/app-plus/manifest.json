{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__98DDB3C", "name": "app", "version": {"name": "1.0.0", "code": "100"}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#F8F8F8"}, "usingComponents": true, "compilerVersion": 3, "distribute": {"google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "apple": {}, "plugins": {"audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#333", "bottom": {"offset": "auto"}}, "uni-app": {"compilerVersion": "4.75", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"borderStyle": "rgba(0,0,0,0.4)", "backgroundColor": "#333", "color": "#8F8F94", "selectedColor": "#f33e54", "list": [{"pagePath": "pages/tabbar/tabbar-1/tabbar-1", "iconPath": "static/img/tabbar/home.png", "selectedIconPath": "static/img/tabbar/homeactive.png", "text": "首页"}, {"pagePath": "pages/tabbar/tabbar-2/tabbar-2", "iconPath": "static/img/tabbar/guanzhu.png", "selectedIconPath": "static/img/tabbar/guanzhuactive.png", "text": "关注"}, {"pagePath": "pages/tabbar/tabbar-3/tabbar-3", "iconPath": "static/img/tabbar/add.png", "selectedIconPath": "static/img/tabbar/addactive.png"}, {"pagePath": "pages/tabbar/tabbar-4/tabbar-4", "iconPath": "static/img/tabbar/news.png", "selectedIconPath": "static/img/tabbar/newsactive.png", "text": "消息"}, {"pagePath": "pages/tabbar/tabbar-5/tabbar-5", "iconPath": "static/img/tabbar/me.png", "selectedIconPath": "static/img/tabbar/meactive.png", "text": "我"}], "height": "50px", "child": ["lauchwebview"], "selected": 0}, "launch_path": "__uniappview.html"}}