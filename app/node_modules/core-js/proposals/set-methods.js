'use strict';
// https://github.com/tc39/proposal-set-methods
require('../modules/esnext.set.difference.v2');
require('../modules/esnext.set.intersection.v2');
require('../modules/esnext.set.is-disjoint-from.v2');
require('../modules/esnext.set.is-subset-of.v2');
require('../modules/esnext.set.is-superset-of.v2');
require('../modules/esnext.set.union.v2');
require('../modules/esnext.set.symmetric-difference.v2');
// TODO: Obsolete versions, remove from `core-js@4`
require('../modules/esnext.set.difference');
require('../modules/esnext.set.intersection');
require('../modules/esnext.set.is-disjoint-from');
require('../modules/esnext.set.is-subset-of');
require('../modules/esnext.set.is-superset-of');
require('../modules/esnext.set.union');
require('../modules/esnext.set.symmetric-difference');
