// tabBar 中间按钮处理 mixin
export default {
  data() {
    return {
      showPublishModal: false
    }
  },

  onLoad() {
    // 在页面加载时设置监听
    this.setupTabBarMidButtonListener();
  },

  methods: {
    setupTabBarMidButtonListener() {
      // 在 App.vue 中全局监听，这里通过事件总线接收
      uni.$on('tabBarMidButtonTap', this.handlePublishClick);
    },

    handlePublishClick() {
      console.log('点击了发布按钮');
      this.showPublishModal = true;
    },

    handlePublishModalClose() {
      this.showPublishModal = false;
    },

    handlePublishTypeSelect(type) {
      console.log('选择了发布类型:', type);
      // 这里可以添加统计或其他逻辑
    }
  },

  beforeDestroy() {
    // 移除监听
    uni.$off('tabBarMidButtonTap', this.handlePublishClick);
  }
}
