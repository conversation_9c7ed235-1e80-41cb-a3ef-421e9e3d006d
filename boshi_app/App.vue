<script>
export default {
  globalData: {
    statusBarHeight: 0
  },
  onLaunch: function() {
    console.log('App Launch')
    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync()
    // 设置全局状态栏高度
    this.globalData.statusBarHeight = systemInfo.statusBarHeight || 0
    console.log('状态栏高度:', this.globalData.statusBarHeight)

    // 监听 tabBar 中间按钮点击
    this.setupTabBarMidButtonListener();
  },
  onShow: function() {
    console.log('App Show')
  },
  onHide: function() {
    console.log('App Hide')
  },

  methods: {
    setupTabBarMidButtonListener() {
      // 监听 tabBar 中间按钮点击事件
      // 注意：这个 API 可能因 uni-app 版本而异
      try {
        uni.onTabBarMidButtonTap && uni.onTabBarMidButtonTap(() => {
          console.log('TabBar 中间按钮被点击');
          uni.$emit('tabBarMidButtonTap');
        });
      } catch (e) {
        console.log('tabBar 中间按钮监听设置失败:', e);
        // 如果 API 不存在，可以通过其他方式实现
        this.setupAlternativeListener();
      }
    },

    setupAlternativeListener() {
      // 备用方案：通过页面路由监听
      console.log('使用备用的 tabBar 中间按钮监听方案');
    }
  }
}
</script>

<style>
/*每个页面公共css */
</style>
