<template>
  <view class="pages">
    <view class="type">
      <view
        class="type-item"
        v-for="(item, index) in typeList"
        :key="index"
        @click="changeType(item)"
        :class="{ active: typeActive === item }"
      >
        <view class="type-text">{{ item }}</view>
      </view>
    </view>
    <view class="page-container">
      <Constellation v-if="typeActive == '星座'"></Constellation>
      <CrapeMyrtle v-if="typeActive == '紫薇'"></CrapeMyrtle>
      <SixLines v-if="typeActive == '六爻'"></SixLines>
    </view>
  </view>
</template>

<script>
import Constellation from "./constellation/constellation.vue";
import CrapeMyrtle from "./crapeMyrtle/crapeMyrtle.vue";
import SixLines from "./sixLines/sixLines.vue";
export default {
  components: {
    Constellation,
    CrapeMyrtle,
    SixLines,
  },
  data() {
    return {
      typeList: ["星座", "紫薇", "六爻"],
      typeActive: "星座",
    };
  },
  onLoad(options) {
    if (!options.name) return;
    console.log('接收到的原始参数:', options.name);

    // 智能解码参数（处理可能的双重编码）
    try {
      let decodedName = options.name;

      // 第一次解码
      decodedName = decodeURIComponent(decodedName);
      console.log('第一次解码后:', decodedName);

      // 检查是否还需要第二次解码（如果包含 %，说明可能是双重编码）
      if (decodedName.includes('%')) {
        try {
          const secondDecode = decodeURIComponent(decodedName);
          console.log('第二次解码后:', secondDecode);
          decodedName = secondDecode;
        } catch (e) {
          console.log('第二次解码失败，使用第一次解码结果');
        }
      }

      console.log('最终解码结果:', decodedName);
      this.typeActive = decodedName;
    } catch (error) {
      console.error('参数解码失败:', error);
      // 如果解码失败，使用原始值
      this.typeActive = options.name;
    }
  },
  methods: {
    changeType(item) {
      this.typeActive = item;
    },
  },
};
</script>

<style lang="scss" scoped>
.pages {
  background-image: url("/static/fortune/<EMAIL>");
  background-color: #1f213d;
  background-size: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.type {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 18rpx 0;

  .type-item {
    background-image: url("/static/fortune/type_no.png");
    background-repeat: no-repeat;
    background-size: 100%;
    height: 70rpx;

    .type-text {
      width: 160rpx;
      height: 56rpx;
      text-align: center;
      line-height: 56rpx;
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.5);
    }

    &.active {
      background-image: url("/static/fortune/type_active.png");

      .type-text {
        width: 160rpx;
        height: 56rpx;
        text-align: center;
        line-height: 56rpx;
        font-size: 28rpx;
        color: #fff;
      }
    }
  }
}

.page-container {
  flex: 1;
}
</style>
