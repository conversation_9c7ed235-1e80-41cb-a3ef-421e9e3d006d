<template>
	<view class="container">
		<view class="" v-if="!hasResult">
			<view class="header">
				<view class="header-title">摇卦结束</view>
			</view>
			<view class="grid-container" @click="shake">
				<image class="grid-item" :class="{ 'rotating': isRotating }" src="/static/fortune/sixLines/coin-back.png" mode="widthFix"></image>
				<image class="grid-item" :class="{ 'rotating': isRotating }" src="/static/fortune/sixLines/coin-front.png" mode="widthFix"></image>
				<image class="grid-item" :class="{ 'rotating': isRotating }" src="/static/fortune/sixLines/coin-back.png" mode="widthFix"></image>
			</view>

			<view class="plate-container">
				<view class="lines-section">
					<view class="line-item">
						<text class="line-label">上爻:</text>
						<text class="line-value">少阳</text>
						<view class="line-bar"></view>
					</view>
					<view class="line-item">
						<text class="line-label">五爻:</text>
						<text class="line-value">老阳</text>
						<view class="line-bar full"></view>
					</view>
					<view class="line-item">
						<text class="line-label">四爻:</text>
						<text class="line-value">少阳</text>
						<view class="line-bar"></view>
					</view>
					<view class="line-item">
						<text class="line-label">三爻:</text>
						<text class="line-value">少阴</text>
						<view class="line-bar split"></view>
					</view>
					<view class="line-item">
						<text class="line-label">二爻:</text>
						<text class="line-value">少阳</text>
						<view class="line-bar"></view>
					</view>
					<view class="line-item">
						<text class="line-label">处爻:</text>
						<text class="line-value">少阴</text>
						<view class="line-bar split"></view>
					</view>
				</view>

				<view class="time-section">
					<text class="time-label">起卦时间:</text>
					<text class="time-value">2025年04月15日 19时31分</text>
					<uni-icons class="time-icon" color="#ffffff99" type="right" size="13"></uni-icons>
				</view>

				<view class="instruction-section">
					<uni-icons class="instruction-icon" color="#ffffff" type="info" size="18"></uni-icons>
					<text class="instruction-text">请集中精力,默想所占之事;点击钱币所在区域,钱币开始旋转后,再次点击可求得一爻,反复六次。</text>
				</view>
			</view>

			<view class="input-section">
				<textarea class="thought-input" auto-height placeholder="请输入您心中的想法 (选填写)" />
			</view>

			<view class="button-section">
				<button class="start-button" @click="startPlating">开始排盘</button>
				<button class="reset-button" @click="resetPlating">重新排盘</button>
			</view>

		</view>
		<Result v-else></Result>

		<view class="disclaimer">
			<text class="disclaimer-icon">①</text>
			<text class="disclaimer-text">当前内容为免费内容,仅供您在娱乐中探索自我,不等于专业测评,不代表价值批判,无任何现实指导意义。</text>
		</view>
	</view>
</template>

<script>
	import Result from "./result.vue"
	export default {
		components: { Result },
		data() {
			return {
				hasResult: false,
				isRotating: false
			};
		},
		methods: {
			shake() {
				console.log('开始排盘 clicked');
				this.isRotating = true;
				setTimeout(() => {
					this.isRotating = false;
				}, 5000);
			},
			startPlating() {
				this.hasResult = true
			},
			resetPlating() {}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 0 40rpx 40rpx;
		color: #ffffff;

		.header {
			text-align: center;
			margin-bottom: 40rpx;
			padding-top: 40rpx;

			.header-title {
				font-weight: 600;
				font-size: 36rpx;
				color: #ffffff;
				line-height: 50rpx;
				margin-bottom: 16rpx;
			}

			.header-subTitle {
				font-weight: 400;
				font-size: 28rpx;
				color: #ffffff99;
				line-height: 32rpx;
			}
		}

		.grid-container {
			display: flex;
			align-items: center;
			justify-content: space-around;
			margin-bottom: 40rpx;
			perspective: 1000px;

			.grid-item {
				width: 126rpx;

				&.rotating {
					animation: rotate3D 5s linear infinite;
				}
			}
		}

		.plate-container {
			background: rgba(23, 19, 47, 0.8);
			border-radius: 24rpx;
			padding: 40rpx 32rpx;

			.lines-section {
				width: 60%;
				margin: 0 auto;

				.line-item {
					display: flex;
					align-items: center;
					margin-bottom: 15rpx;

					.line-label {
						font-weight: 600;
						font-size: 28rpx;
						color: #a45dff;
						width: 84rpx;
					}

					.line-value {
						font-weight: 600;
						font-size: 28rpx;
						color: #ffffff;
						margin-right: 25rpx;
					}

					.line-bar {
						flex: 1;
						height: 24rpx;
						background-color: #a45dff;
						border-radius: 10rpx;

						&.full {
							background-color: #e5a955;
						}

						&.split {
							display: flex;
							justify-content: space-between;
							background-color: transparent;

							&::before,
							&::after {
								content: "";
								display: block;
								width: 46%;
								height: 100%;
								background-color: #4a00a9;
								border-radius: 10rpx;
							}
						}
					}
				}
			}

			.time-section {
				display: flex;
				align-items: center;
				padding: 0 25rpx;
				margin: 40rpx 0;
				height: 60rpx;
				background: #403c60;
				border-radius: 12rpx;

				.time-label {
					font-weight: 600;
					font-size: 28rpx;
					color: #ffffff;
				}

				.time-value {
					font-size: 26rpx;
					flex: 1;
					color: #ffffff99;
					line-height: 36rpx;
					text-align: right;
				}

				.time-icon {}
			}

			.instruction-section {
				display: flex;
				align-items: flex-start;

				.instruction-icon {
					color: #9086ae;
					margin-right: 10rpx;
					line-height: 32rpx;
				}

				.instruction-text {
					flex: 1;
					font-weight: 400;
					font-size: 24rpx;
					color: #ffffff;
					line-height: 32rpx;
				}
			}
		}

		.input-section {
			margin-top: 16rpx;
			height: 180rpx;
			background: rgba(23, 19, 47, 0.8);
			border-radius: 24rpx;
			padding: 30rpx;

			.thought-input {
				height: calc(100% - 20rpx);
				font-weight: 400;
				font-size: 26rpx;
				color: #ffffff;
				width: 100%;
			}
		}

		.button-section {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-top: -40rpx;

			.start-button {
				background: linear-gradient(to right, #f7b500, #f2c45b);
				color: #333333;
				font-size: 32rpx;
				font-weight: 600;
				border-radius: 50rpx;
				padding: 25rpx 0;
				margin-bottom: 20rpx;
				line-height: normal;
				width: 308rpx;
				height: 94rpx;
			}

			.reset-button {
				background: none;
				color: rgba(255, 255, 255, 0.6);
				font-size: 28rpx;
				font-weight: 400;
				border: none;
				padding: 0;
				line-height: normal;
			}
		}

		.disclaimer {
			font-size: 24rpx;
			color: rgba(255, 255, 255, 0.6);
			padding: 33rpx 0 56rpx;
			text-align: center;
			display: flex;
			align-items: flex-start;

			.disclaimer-icon {
				font-size: 22rpx;
				margin-right: 10rpx;
			}

			.disclaimer-text {
				flex: 1;
				text-align: left;
			}
		}

		.detail-section,
		.detail-block,
		.detail-title,
		.detail-outline,
		.detail-text {
			display: none;
		}
	}

	@keyframes rotate3D {
		0% {
			transform: rotateY(0deg);
		}

		100% {
			transform: rotateY(3600deg);
		}
	}
</style>