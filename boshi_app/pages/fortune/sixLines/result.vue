<template>
	<view class="result-container">
		<view class="header">
			<view class="header-title">
				<image src="/static/fortune/sixLines/coin-icon.png" mode="widthFix"></image> 六爻排盘
			</view>
		</view>

		<view class="lines-section">
			<view class="line-item">
				<text class="line-label">上爻:</text>
				<text class="line-value">少阳</text>
				<view class="line-bar"></view>
			</view>
			<view class="line-item">
				<text class="line-label">五爻:</text>
				<text class="line-value">老阳</text>
				<view class="line-bar full"></view>
			</view>
			<view class="line-item">
				<text class="line-label">四爻:</text>
				<text class="line-value">少阳</text>
				<view class="line-bar"></view>
			</view>
			<view class="line-item">
				<text class="line-label">三爻:</text>
				<text class="line-value">少阴</text>
				<view class="line-bar split"></view>
			</view>
			<view class="line-item">
				<text class="line-label">二爻:</text>
				<text class="line-value">少阳</text>
				<view class="line-bar"></view>
			</view>
			<view class="line-item">
				<text class="line-label">处爻:</text>
				<text class="line-value">少阴</text>
				<view class="line-bar split"></view>
			</view>
		</view>
		<view class="user-section">
			<view class="user-item">
				<text class="user-label">求测人生年:</text> <text class="user-value">2025年</text>
			</view>
			<view class="user-item">
				<text class="user-label">性别:</text> <text class="user-value">男</text>
			</view>
			<view class="user-item">
				<text class="user-label">起卦方式:</text> <text class="user-value">手工摇卦</text>
			</view>
			<view class="user-item">
				<text class="user-label">公历:</text> <text class="user-value">2025-04-15 19:31</text>
			</view>
			<view class="user-item">
				<text class="user-label">农历:</text> <text class="user-value">二零二五年三月十八</text>
			</view>
			<view class="user-item">
				<text class="user-label">干支:</text> <text class="user-value">乙巳年 庚辰月 甲寅日 甲戌时</text>
			</view>
			<view class="user-item">
				<text class="user-label">神煞:</text> <text class="user-value">文昌贵人 亡神 金舆 勾绞煞 寡宿 犯天罗 天喜 禄神 孤鸾煞 劫煞 血刃 披麻八专</text>
			</view>
		</view>

		<view class="liuShen-section">
			<view class="liuShen-item title">
				<view class="liuShen-label">六神</view>
				<view class="liuShen-value">本卦：天水讼</view>
				<view class="liuShen-value">变卦：火水未济</view>
			</view>
			<view class="liuShen-item" v-for="item in 6" :key="item.index">
				<view class="liuShen-label">玄武</view>
				<view class="liuShen-value">子孙壬戌土</view>
				<view class="liuShen-value">兄弟己已火</view>
			</view>
		</view>

		<view class="explain-section">
			<view class="explain-title">解释说明</view>
			<view class="explain-content">【讼卦】
				【中下卦】天水讼（讼卦）慎争戒讼
				第六卦：讼卦（天水讼）
				讼。有孚，窒惕，中吉，终凶。利见大人，不利涉大川。
				象曰：天与水违行，讼。君子以做事谋始。
				白话文解释：
				讼卦：虽有利可图（获得俘虏），但要警惕戒惧。其事中间吉利，后来凶险。占筮得此爻，有利于会见贵族王公，不利于涉水源河。
				《象辞》说：上卦为乾，乾为天；下卦为坎，坎为水，天水隔绝，流向相背，事理乖
				舛，这是讼卦的卦象。君子观此卦象，以杜绝争讼为意，从而在谋事之初必须慎之又慎。</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isRotating: false
			};
		},
		methods: {
			shake() {
				console.log('开始排盘 clicked');
				this.isRotating = true;
				setTimeout(() => {
					this.isRotating = false;
				}, 5000);
			},
			startPlating() {

			},
			resetPlating() {}
		}
	}
</script>

<style lang="scss" scoped>
	.result-container {
		color: #ffffff;

		.header {
			text-align: center;
			margin-bottom: 40rpx;

			.header-title {
				font-weight: 600;
				font-size: 36rpx;
				color: #ffffff;
				line-height: 50rpx;
				margin-bottom: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				image {
					width: 44rpx;
					margin-right: 10rpx;
				}
			}

			.header-subTitle {
				font-weight: 400;
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.6);
				line-height: 32rpx;
			}
		}

		.lines-section {
			background: rgba(23, 19, 47, 0.8);
			border-radius: 24rpx;
			padding: 40rpx 32rpx;

			.line-item {
				width: 60%;
				margin: 0 auto 20rpx;
				display: flex;
				align-items: center;

				.line-label {
					font-weight: 600;
					font-size: 28rpx;
					color: #a45dff;
					width: 84rpx;
				}

				.line-value {
					font-weight: 600;
					font-size: 28rpx;
					color: #ffffff;
					margin-right: 25rpx;
				}

				.line-bar {
					flex: 1;
					height: 24rpx;
					background-color: #a45dff;
					border-radius: 10rpx;
					transition: all 0.3s ease;

					&.full {
						background-color: #e5a955;
					}

					&.split {
						display: flex;
						justify-content: space-between;
						background-color: transparent;

						&::before,
						&::after {
							content: "";
							display: block;
							width: 46%;
							height: 100%;
							background-color: #4a00a9;
							border-radius: 10rpx;
						}
					}
				}
			}
		}

		.user-section {
			background: rgba(23, 19, 47, 0.8);
			border-radius: 24rpx;
			padding: 40rpx 32rpx;
			margin-top: 16rpx;

			.user-item {
				font-size: 24rpx;
				line-height: 36rpx;

				.user-label {
					color: #ffffff;
					font-weight: 600;
					margin-right: 10rpx;
				}
			}
		}

		.liuShen-section {
			background: rgba(23, 19, 47, 0.8);
			border-radius: 24rpx;
			padding: 40rpx 32rpx;
			margin-top: 16rpx;

			.liuShen-item {
				font-size: 24rpx;
				color: #FFFFFF;
				line-height: 36rpx;
				display: flex;
				align-items: center;

				&.title {
					font-weight: 600;
				}

				.liuShen-label {
					width: 80rpx;
				}

				.liuShen-value {
					width: 200rpx;
				}
			}

		}

		.explain-section {
			margin-top: 16rpx;

			.explain-title {
				font-weight: 600;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 45rpx;
			}

			.explain-content {
				background: rgba(23, 19, 47, 0.8);
				border-radius: 24rpx;
				padding: 40rpx 32rpx;
				margin-top: 10rpx;
				font-weight: 600;
				font-size: 24rpx;
				color: #FFFFFF;
				line-height: 36rpx;
			}
		}

	}
</style>