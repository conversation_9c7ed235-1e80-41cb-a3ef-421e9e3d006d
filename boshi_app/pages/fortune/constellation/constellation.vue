<template>
	<view class="container">
		<view class="topText">
			<view class="starSignTitle">
				日狮子·月巨蟹·升天秤的自己
			</view>
			<view class="starSignText">
				不打扮不出门的表演艺术家，内心住着爱哭鬼
			</view>
		</view>
		<view class="describeBox">
			<view class="describeText">
				<view class="describeText1">
					不一样的烟火
				</view>
				<view class="describeText2">
					人生之路漫漫，唯一课题是要活出独一无二的
				</view>
			</view>
			<view class="describeList">
				<view class="describeItem" style="color: #D483CA ;">
					<view class="describeItemText">
						桃花爆炸多
					</view>
					<view class="describeItemImg">
						<text>爱情</text>
					</view>
				</view>
				<view class="describeItem" style="color: #8E83D4 ;">
					<view class="describeItemText">
						当代金饭碗
					</view>
					<view class="describeItemImg1">
						<text>事业</text>
					</view>
				</view>
				<view class="describeItem" style="color: #D4A083 ;">
					<view class="describeItemText">
						意外之财
					</view>
					<view class="describeItemImg2"> <text>财富</text> </view>
				</view>
				<view class="describeItem" style="color: #83D48F  ;">
					<view class="describeItemText"> 气氛托举王 </view>
					<view class="describeItemImg3"> <text>人际关系</text> </view>
				</view>

			</view>
		</view>
		<view class="gridBox">
			<view class="gridItem" v-for="(item,index) in gridList" :key="index">
				<view class="" v-if="index == 4 ">
					<view class="gridItemBgImg1">
						<image :src="item.image" mode="aspectFit"></image>
					</view>
				</view>
				<view class="" v-else>
					<view class="gridItemBgImg">
						<view class="gridItemContent">
							<view class="gridItemTitle">
								{{item.title}}
							</view>
							<view class="gridItemDescription" :style="{ 'color': item.color }">
								<image :src="item.image" mode="aspectFit"></image>
								<text>{{item.description}}</text>
							</view>
						</view>
					</view>

				</view>
			</view>
		</view>
		<view class="passwordBox">
			<view class="passwordTitle">
				星座密码
			</view>
			<view class="passwordContent">
				<view class="twoPasswordContent">
					<view class="LeftPasswordContent">
						<!-- <image src="/static/fortune/constellation/矩形@2x(5).png" mode="aspectFit"></image> -->
						<view class="LeftPasswordContentText">
							<view class="LeftPasswordContentText1">
								<image src="/static/fortune/constellation/表面***********" mode="aspectFit"></image>
								表面的自己
							</view>
							<view class="LeftPasswordContentText2">
								和平使者
							</view>
							<view class="LeftPasswordContentText2">
								调节矛盾不在话下
							</view>
						</view>
					</view>
					<view class="rightPasswordContent">
						<!-- <image src="/static/fortune/constellation/矩形@2x(1).png" mode="aspectFit"></image> -->
						<view class="rightPasswordContentText">
							<view class="rightPasswordContentText1">
								<image src="/static/fortune/constellation/实际***********" mode="aspectFit"></image>
								实际的自己
							</view>
							<view class="rightPasswordContentText2">
								不要误伤到本秤
							</view>
						</view>
					</view>

				</view>
				<view class="passwordContentOne">
					<!-- <image src="/static/fortune/constellation/矩形@2x(3).png" mode="aspectFit"></image> -->
					<view class="passwordContentOneText">
						<view class="passwordContentOneText1">
							<image src="/static/fortune/constellation/技能***********" mode="aspectFit"></image>
							隐藏技能
						</view>
						<view class="passwordContentOneText2">
							对生活高要求的品质把控者
						</view>
					</view>
				</view>
				<view class="passwordContentTwo">
					<!-- <image src="/static/fortune/constellation/矩形@2x(3).png" mode="aspectFit"></image> -->
					<view class="passwordContentTwoText">
						<view class="passwordContentTwoText1">
							<image src="/static/fortune/constellation/表面@2x.png" mode="aspectFit"></image>
							契合度最高的星座
						</view>
						<view class="passwordContentTwoText2">
							白羊座、天秤座
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="todayTips">
			<view class="todayTipsTitle">
				给自己的今日提醒
			</view>
			<view class="todayTipsContent">
				<view class="todayTipsContentItem">
					<view class="">
						<image src="/static/fortune/constellation/幸运色***********" mode="aspectFit"></image>
					</view>
					<view class="todayTipsContentItemText">
						<view class="">
							幸运色
						</view>
						<view class="" style="color: #FFC262;">
							黄色
						</view>
					</view>
				</view>
				<view class="todayTipsContentItem">
					<view class="">
						<image src="/static/fortune/constellation/幸运数***********" mode="aspectFit"></image>
					</view>
					<view class="todayTipsContentItemText">
						<view class="">
							幸运数字
						</view>
						<view class="" style="color: #FFC262;">
							18.8
						</view>
					</view>
				</view>
				<view class="todayTipsContentItem">
					<view class="">
						<image src="/static/fortune/constellation/幸运食物***********" mode="aspectFit"></image>
					</view>
					<view class="todayTipsContentItemText">
						<view class="">
							幸运食物
						</view>
						<view class="" style="color: #FFC262;">
							泡面
						</view>
					</view>
				</view>

			</view>
			<view class="TwotodayTipsContent">
				<view class="TwotodayTipsContentItem">
					<view class="f1">
						建议
					</view>
					<view class="f2">
						把酒言欢，观察世界
					</view>
				</view>
				<view class="TwotodayTipsContentItem">
					<view class="f1">
						避免
					</view>
					<view class="f2">
						不留情面，抬杠
					</view>
				</view>

			</view>

		</view>
		<view class="switchTabs">
			<view class="TabsBox">
				<view class="tab-bar">
					<view class="tab-item" @click="SwitchTabs(1)">
						星座
						<view class="actTabitem" v-if="tabsShow1 == 1">
						</view>
					</view>
					<view class="tab-item" @click="SwitchTabs(2)">
						宫位
						<view class="actTabitem" v-if="tabsShow1 == 2">
						</view>
					</view>
				</view>
			</view>
			<view class="TabsContent">
				<view class="TabsContentItem" v-for="(item,index) in List" :key="index">
					<view class="TabsContentItemTop">
						<image :src="item.image" mode="aspectFit"></image>
						{{item.title}}
					</view>
					<view class="TabsContentItemText">
						{{item.description}}
					</view>
				</view>
			</view>
		</view>
		<view class="pageTips">
			<view class="pageTipsImg">
				<image src="/static/fortune/constellation/注意@2x.png" mode="aspectFit"></image>
			</view>
			<view class="pageTipsText">
				当前内容为免费内容，仅供您在娱乐中探索自我，不等于专业测评，不代表价值批判，无任何显示指导意义。
			</view>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabsShow1: 1,
				gridList: [{
						image: '/static/fortune/constellation/<EMAIL>',
						title: '表演型人格',
						description: '太阳狮子',
						color: '#FF88BC'
					},
					{
						image: '/static/fortune/constellation/月亮@2x.png',
						title: '无比感性',
						description: '月亮巨蟹',
						color: '#BEBDFF'
					},
					{
						image: '/static/fortune/constellation/<EMAIL>',
						title: '亲和力Max',
						description: '上升天秤',
						color: '#FF88BC'
					},
					{
						image: '/static/fortune/constellation/女@2x.png',
						title: '纠结狂',
						description: '金星处女',
						color: '#CAD4FF'
					},
					{
						image: '/static/fortune/constellation/太阳狮子@2x.png',
						title: '',
						description: '',
						color: ''
					},

					{
						image: '/static/fortune/constellation/男@2x.png',
						title: '和事佬',
						description: '火星天秤',
						color: '#CAD4FF'
					},
					{
						image: '/static/fortune/constellation/水星@2x.png',
						title: '我思故我在',
						description: '水星处女',
						color: '#CAD4FF'
					},
					{
						image: '/static/fortune/constellation/北交@2x.png',
						title: '无比感性',
						description: '北交处女',
						color: '#CAD4FF'
					},
					{
						image: '/static/fortune/constellation/婚神@2x.png',
						title: '霸道总裁',
						description: '婚神狮子',
						color: '#CAD4FF'
					}
				],
				List: [{
						image: '/static/fortune/constellation/太阳狮子@2x.png',
						title: '太阳·狮子',
						description: '今天你严肃而又认证，似乎要比之前更加成熟稳重了，这种情况下，也非常适合找合适的人，去探讨一些深入的问题。',
					},
					{
						image: '/static/fortune/constellation/太阳狮子@2x.png',
						title: '太阳·狮子',
						description: '今天你严肃而又认证，似乎要比之前更加成熟稳重了，这种情况下，也非常适合找合适的人，去探讨一些深入的问题。',
					},
					{
						image: '/static/fortune/constellation/太阳狮子@2x.png',
						title: '太阳·狮子',
						description: '今天你严肃而又认证，似乎要比之前更加成熟稳重了，这种情况下，也非常适合找合适的人，去探讨一些深入的问题。',
					},
					{
						image: '/static/fortune/constellation/太阳狮子@2x.png',
						title: '太阳·狮子',
						description: '今天你严肃而又认证，似乎要比之前更加成熟稳重了，这种情况下，也非常适合找合适的人，去探讨一些深入的问题。',
					},
				]

			};
		},
		methods: {
			SwitchTabs(val) {
				console.log(val);
				this.tabsShow1 = val
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.topText {
			margin: 16rpx auto;
			text-align: center;

			.starSignTitle {
				font-weight: 600;
				font-size: 36rpx;
				color: #FFFFFF;
			}

			.starSignText {
				font-weight: 400;
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.6);
				margin-top: 16rpx;
			}
		}

		.describeBox {


			.describeBg {
				height: 350rpx;
				position: relative;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.describeText {
				padding: 24rpx 48rpx;
				background-image: url('/static/fortune/constellation/文字背景@2x.png');
				background-size: 100%;
				height: 100%;

				.describeText1 {
					font-weight: 600;
					font-size: 30rpx;
					color: #CEC4B2;
				}

				.describeText2 {
					font-weight: 400;
					font-size: 26rpx;
					color: #969188;
					margin-top: 8rpx;
				}
			}

			.describeList {
				padding: 24rpx 48rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				gap: 24rpx;

				.describeItem {
					.describeItemText {
						font-weight: 400;
						font-size: 26rpx;
						margin-bottom: 27rpx;
					}

					.describeItemImg {
						width: 150rpx;
						height: 134rpx;
						font-weight: 400;
						font-size: 20rpx;
						background-image: url('/static/fortune/constellation/爱情线条@2x.png');
						background-size: 100% 100%;

						// text {
						display: flex;
						align-items: flex-end;
						justify-content: center;
						// }
					}

					.describeItemImg1 {
						width: 150rpx;
						height: 134rpx;
						font-weight: 400;
						font-size: 20rpx;
						background-image: url('/static/fortune/constellation/爱情线条@2x(1).png');
						background-size: 100% 100%;

						display: flex;
						align-items: flex-end;
						justify-content: center;
					}

					.describeItemImg2 {
						width: 150rpx;
						height: 134rpx;
						font-weight: 400;
						font-size: 20rpx;
						background-image: url('/static/fortune/constellation/爱情线条@2x(2).png');
						background-size: 100% 100%;


						display: flex;
						align-items: flex-end;
						justify-content: center;
					}

					.describeItemImg3 {
						width: 150rpx;
						height: 134rpx;
						font-weight: 400;
						font-size: 20rpx;
						background-image: url('/static/fortune/constellation/爱情线条@2x(3).png');
						background-size: 100% 100%;

						display: flex;
						align-items: flex-end;
						justify-content: center;
					}
				}
			}
		}

		.gridBox {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-around;
			padding: 20rpx;

			.gridItem {
				.gridItemBgImg1 {
					width: 196rpx;
					height: 98rpx;
					margin-bottom: 32rpx;

					image {
						width: 100%;
						height: 100%;
					}
				}

				// position: relative;

				.gridItemBgImg {
					background-image: url('/static/fortune/constellation/按钮*********');
					background-size: 100% 100%;
					width: 196rpx;
					height: 98rpx;
					margin-bottom: 32rpx;

					image {
						width: 100%;
						height: 100%;
					}
				}

				.gridItemContent {
					width: 100%;
					text-align: center;
					padding-top: 16rpx;


					.gridItemTitle {
						font-weight: 400;
						font-size: 24rpx;
						color: #FFFFFF;
						text-align: center;
						// width: 180rpx;
					}

					.gridItemDescription {
						display: flex;
						align-items: center;
						justify-content: center;

						image {
							width: 24rpx;
							height: 24rpx;
						}

						text {
							font-weight: 400;
							font-size: 24rpx;

						}
					}


				}


			}
		}

		.passwordBox {
			padding: 10rpx 48rpx;

			.passwordTitle {
				font-weight: 600;
				font-size: 32rpx;
				color: #CAD4FF;
				margin-bottom: 16rpx;
			}

			.passwordContent {
				.twoPasswordContent {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.LeftPasswordContent {
						.LeftPasswordContentText {
							width: 315rpx;
							height: 164rpx;
							background-image: url('/static/fortune/constellation/矩形@2x(5).png');
							background-size: 100% 100%;
							padding: 24rpx;

							.LeftPasswordContentText1 {
								display: flex;
								align-items: center;
								font-weight: 600;
								font-size: 28rpx;
								color: #BAC2E8;
								margin-bottom: 8rpx;

								image {
									width: 32rpx;
									height: 32rpx;
								}
							}

							.LeftPasswordContentText2 {
								font-weight: 400;
								font-size: 24rpx;
								color: #BAC2E8;
								margin-top: 4rpx;
							}
						}
					}

					.rightPasswordContent {

						.rightPasswordContentText {
							width: 315rpx;
							height: 164rpx;
							background-image: url('/static/fortune/constellation/矩形@2x(5).png');
							background-size: 100% 100%;
							padding: 24rpx;

							.rightPasswordContentText1 {
								display: flex;
								align-items: center;
								font-weight: 600;
								font-size: 28rpx;
								color: #BAC2E8;
								margin-bottom: 8rpx;

								image {
									width: 32rpx;
									height: 32rpx;
								}
							}

							.rightPasswordContentText2 {
								font-weight: 400;
								font-size: 24rpx;
								color: #BAC2E8;
								margin-bottom: 4rpx;
							}
						}
					}
				}

				.passwordContentOne {
					margin-top: 24rpx;
					height: 127rpx;
					background-image: url('/static/fortune/constellation/矩形@2x(3).png');
					background-size: 100% 100%;

					// image {
					//   width: 654rpx;
					//   height: 127rpx;
					//   position: relative;
					// }

					.passwordContentOneText {
						padding: 24rpx;
						// position: absolute;
						// left: 70rpx;
						// top: 1300rpx;

						.passwordContentOneText1 {
							display: flex;
							align-items: center;
							font-weight: 600;
							font-size: 28rpx;
							color: #BAC2E8;

							image {
								width: 32rpx;
								height: 32rpx;
							}
						}

						.passwordContentOneText2 {
							margin-top: 4rpx;
							font-weight: 400;
							font-size: 24rpx;
							color: #BAC2E8;
						}
					}

				}

				.passwordContentTwo {
					margin-top: 24rpx;
					height: 127rpx;
					background-image: url('/static/fortune/constellation/矩形@2x(3).png');
					background-size: 100% 100%;

					.passwordContentTwoText {
						padding: 24rpx;

						.passwordContentTwoText1 {
							display: flex;
							align-items: center;
							font-weight: 600;
							font-size: 28rpx;
							color: #BAC2E8;

							image {
								width: 32rpx;
								height: 32rpx;
							}
						}

						.passwordContentTwoText2 {
							font-weight: 400;
							font-size: 24rpx;
							color: #BAC2E8;
							margin-top: 4rpx;
						}
					}

				}

			}
		}


		.todayTips {
			padding: 10rpx 48rpx;

			.todayTipsTitle {
				font-weight: 600;
				font-size: 32rpx;
				color: #CAD4FF;
				margin-bottom: 16rpx;
			}

			.todayTipsContent {
				height: 127rpx;
				background-image: url('/static/fortune/constellation/矩形@2x(3).png');
				background-size: 100% 100%;
				display: flex;
				align-items: center;
				justify-content: space-around;
				// background-color: #4F4473;
				border-radius: 20rpx;
				height: 128rpx;
				width: 100%;

				.todayTipsContentItem {
					display: flex;
					align-items: center;


					image {
						width: 72rpx;
						height: 72rpx;
						margin-right: 12rpx;
					}

					.todayTipsContentItemText {
						font-weight: 400;
						font-size: 24rpx;
						color: #BAC2E8;
					}
				}
			}

			.TwotodayTipsContent {
				margin-top: 23rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;


				.TwotodayTipsContentItem {
					// background-color: #4F4473;
					border-radius: 20rpx;
					height: 128rpx;
					width: 48%;
					padding: 20rpx 24rpx;
					background-image: url('/static/fortune/constellation/矩形@2x(3).png');
					background-size: 100% 100%;
					display: flex;

					.f1 {
						font-weight: 600;
						font-size: 28rpx;
						color: #BAC2E8;
					}

					.f2 {
						font-weight: 400;
						font-size: 24rpx;
						color: #7885C4;
					}
				}
			}
		}

		.switchTabs {

			.TabsBox {
				padding: 20rpx;
				background-color: #1a1a2e;


				.tab-bar {
					display: flex;

					.tab-item {
						padding: 20rpx 30rpx;
						color: #ccc;
						cursor: pointer;

						.actTabitem {
							width: 40rpx;
							height: 4rpx;
							background: #FFFFFF;
							border-radius: 4rpx;
							margin: 8rpx auto;
						}
					}
				}


			}

			.TabsContent {
				padding: 24rpx 48rpx;

				.TabsContentItem {
					width: 654rpx;
					height: 200rpx;
					padding: 28rpx 24rpx;
					background: rgba(33, 27, 52, 0.6);
					border-radius: 24rpx;
					border: 1rpx solid #635873;
					margin-bottom: 16rpx;

					.TabsContentItemTop {
						display: flex;
						align-items: center;
						font-weight: 600;
						font-size: 28rpx;
						color: #FFFFFF;

						image {
							width: 32rpx;
							height: 32rpx;
							margin-right: 8rpx;
						}
					}

					.TabsContentItemText {
						margin-top: 20rpx;
						font-weight: 400;
						font-size: 24rpx;
						color: #9086AE;
					}
				}
			}
		}

		.pageTips {
			padding: 0 48rpx 80rpx;
			display: flex;
			// align-items: center;

			.pageTipsImg image {
				width: 24rpx;
				height: 24rpx;
				margin-right: 8rpx;
			}

			.pageTipsText {
				font-weight: 400;
				font-size: 24rpx;
				color: #FFFFFF;
			}
		}
	}
</style>