<template>
  <view class="login">
    <view class="logo">
      <view class="logo-icon"></view>
      <view class="logo-title">铂时商城</view>
    </view>
    <view class="form">
      <view>
        <Input
          v-model="phone"
          placeholder="请输入您的手机号"
          icon="/static/icon/phone_icon.png"
          type="number"
        />
        <Input
          v-model="code"
          placeholder="请输入验证码"
          icon="/static/icon/password_icon.png"
          type="number"
          style="margin-top: 24rpx"
        >
          <template v-slot:right>
            <view
              class="code"
              @click="getCode"
              :style="{
                backgroundColor: countdown > 0 ? '#E1CCFE' : '#7414ff',
              }"
              >{{ codeText }}
            </view>
          </template>
        </Input>
        <!--<Input-->
        <!--  v-model="password"-->
        <!--  placeholder="请输入您的密码"-->
        <!--  icon="/static/icon/password.png"-->
        <!--  :type="passwordVisible ? 'text' : 'password'"-->
        <!--  style="margin-top: 24rpx"-->
        <!--  @input="onPasswordInput"-->
        <!--&gt;-->
        <!--  <template v-slot:right>-->
        <!--    <image-->
        <!--      :src="-->
        <!--        passwordVisible-->
        <!--          ? '/static/icon/visible.png'-->
        <!--          : '/static/icon/invisible.png'-->
        <!--      "-->
        <!--      @click="togglePasswordVisible"-->
        <!--      style="width: 40rpx; height: 40rpx"-->
        <!--    ></image>-->
        <!--  </template>-->
        <!--</Input>-->
        <!--<Input-->
        <!--  v-model="confirmPassword"-->
        <!--  placeholder="请再次输入您的密码"-->
        <!--  icon="/static/icon/再次输入密码.png"-->
        <!--  :type="confirmPasswordVisible ? 'text' : 'password'"-->
        <!--  style="margin-top: 24rpx"-->
        <!--  :class="{-->
        <!--    'password-match': passwordMatchStatus === 'match',-->
        <!--    'password-mismatch': passwordMatchStatus === 'mismatch',-->
        <!--  }"-->
        <!--  @input="onConfirmPasswordInput"-->
        <!--&gt;-->
        <!--  <template v-slot:right>-->
        <!--    <view style="display: flex; align-items: center; gap: 16rpx">-->
        <!--      &lt;!&ndash; 密码匹配状态图标 &ndash;&gt;-->
        <!--      <view v-if="passwordMatchStatus === 'match'" class="match-icon"-->
        <!--        >✓</view-->
        <!--      >-->
        <!--      <view-->
        <!--        v-if="passwordMatchStatus === 'mismatch'"-->
        <!--        class="mismatch-icon"-->
        <!--        >✗</view-->
        <!--      >-->
        <!--      &lt;!&ndash; 密码可见性切换图标 &ndash;&gt;-->
        <!--      <image-->
        <!--        :src="-->
        <!--          confirmPasswordVisible-->
        <!--            ? '/static/icon/visible.png'-->
        <!--            : '/static/icon/invisible.png'-->
        <!--        "-->
        <!--        @click="toggleConfirmPasswordVisible"-->
        <!--        style="width: 40rpx; height: 40rpx"-->
        <!--      ></image>-->
        <!--    </view>-->
        <!--  </template>-->
        <!--</Input>-->
        <!-- 密码确认提示文字 -->
        <view
          v-if="passwordMatchStatus === 'mismatch'"
          class="password-tip error"
        >
          两次密码输入不一致
        </view>
        <view
          v-if="passwordMatchStatus === 'match'"
          class="password-tip success"
        >
          密码确认成功
        </view>
      </view>
      <view class="login-btn">
        <view @click="goToLogin">登录</view>
      </view>
      <view
        class="submit-btn"
        :style="{ backgroundColor: canRegister ? '#7414FF' : '#c3c3c3' }"
        @click="submitRegister"
      >
        注册
      </view>
    </view>
    <!--<view class="other-login">-->
    <!--  <view class="other-title">其他登录方式</view>-->
    <!--  <image-->
    <!--    src="/static/loginSignup/weChat.png"-->
    <!--    style="width: 110rpx; height: 110rpx"-->
    <!--  ></image>-->
    <!--</view>-->
    <view class="agreement"
      >注册即代表你同意
      <text class="color-7414FF">《用户协议》</text>
      与
      <text class="color-7414FF">《隐私政策》</text>
      以及
      <text class="color-7414FF">《中国移动认证服务条款》</text>
    </view>
  </view>
</template>

<script>
import Input from "@/components/common/Input.vue";
// 不再需要导入工具函数，直接使用 uni API

export default {
  components: { Input },
  data() {
    return {
      isRegister: false,
      isAccountLogin: false,
      username: "",
      password: "",
      confirmPassword: "",
      phone: "",
      code: "",
      countdown: 0,
      timer: null,
      passwordVisible: false,
      confirmPasswordVisible: false,
    };
  },
  computed: {
    codeText() {
      return this.countdown > 0 ? `${this.countdown}S` : "获取验证码";
    },
    canRegister() {
      // 注册条件：手机号非空、验证码长度>=4、密码长度>=6、两次密码一致
      return (
        this.phone !== "" && this.code.length >= 4
        // &&
        // this.password.length >= 6 &&
        // this.confirmPassword.length >= 6 &&
        // this.password === this.confirmPassword
      );
    },
    // 密码确认状态
    passwordMatchStatus() {
      if (this.confirmPassword === "") {
        return ""; // 未输入
      }
      return this.password === this.confirmPassword ? "match" : "mismatch";
    },
  },
  methods: {
    // 跳转到登录页面
    goToLogin() {
      console.log('点击登录按钮');

      // 获取当前页面栈
      const pages = getCurrentPages();
      console.log('当前页面栈长度:', pages.length);

      // 检查页面栈中是否有登录页面
      let hasLoginPage = false;
      if (pages.length > 1) {
        for (let i = pages.length - 2; i >= 0; i--) {
          console.log(`页面栈[${i}]:`, pages[i].route);
          if (pages[i].route === 'pages/login/login') {
            hasLoginPage = true;
            console.log('发现登录页面在页面栈中，使用 navigateBack');
            uni.navigateBack({
              delta: pages.length - 1 - i
            });
            return;
          }
        }
      }

      if (!hasLoginPage) {
        console.log('页面栈中没有登录页面，使用 navigateTo 新开');
        uni.navigateTo({
          url: '/pages/login/login'
        });
      }
    },
    getCode() {
      if (this.countdown > 0) {
        return;
      }
      this.countdown = 60;
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(this.timer);
          this.timer = null;
        }
      }, 1000);
    },
    togglePasswordVisible() {
      this.passwordVisible = !this.passwordVisible;
    },
    toggleConfirmPasswordVisible() {
      this.confirmPasswordVisible = !this.confirmPasswordVisible;
    },
    // 密码输入验证 - 只允许大小写字母、数字、下划线
    validatePasswordInput(value) {
      // 正则表达式：只允许大小写字母、数字、下划线
      const regex = /^[a-zA-Z0-9_]*$/;
      return regex.test(value);
    },
    // 密码输入事件处理
    onPasswordInput(value) {
      if (this.validatePasswordInput(value)) {
        this.password = value;
      } else {
        // 过滤掉不符合规则的字符
        this.password = value.replace(/[^a-zA-Z0-9_]/g, "");
        uni.showToast({
          title: "密码只能包含字母、数字和下划线",
          icon: "none",
          duration: 1500,
        });
      }
    },
    // 确认密码输入事件处理
    onConfirmPasswordInput(value) {
      if (this.validatePasswordInput(value)) {
        this.confirmPassword = value;
      } else {
        // 过滤掉不符合规则的字符
        this.confirmPassword = value.replace(/[^a-zA-Z0-9_]/g, "");
        uni.showToast({
          title: "密码只能包含字母、数字和下划线",
          icon: "none",
          duration: 1500,
        });
      }
    },
    submitRegister() {
      if (!this.canRegister) {
        // 根据具体情况给出不同的提示
        if (this.phone === "") {
          uni.showToast({
            title: "请输入手机号",
            icon: "none",
          });
        } else if (this.code.length < 4) {
          uni.showToast({
            title: "请输入正确的验证码",
            icon: "none",
          });
        }
        // else if (this.password.length < 6) {
        //   uni.showToast({
        //     title: "密码长度至少6位",
        //     icon: "none",
        //   });
        // } else if (this.confirmPassword.length < 6) {
        //   uni.showToast({
        //     title: "请确认密码",
        //     icon: "none",
        //   });
        // } else if (this.password !== this.confirmPassword) {
        //   uni.showToast({
        //     title: "两次密码输入不一致",
        //     icon: "none",
        //   });
        // }
        else {
          uni.showToast({
            title: "请填写完整信息",
            icon: "none",
          });
        }
        return;
      }

      // 注册逻辑
      console.log("注册信息:", {
        phone: this.phone,
        code: this.code,
        password: this.password,
      });
      // 这里可以调用注册API
      uni.showToast({
        title: "注册成功",
        icon: "success",
      });
      // 注册成功后跳转首页
      setTimeout(() => {
        uni.switchTab({
          url: "/pages/index/index",
        });
      }, 1500);
    },
  },
};
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100vh;
  position: relative;
  box-sizing: border-box;
  background-image: url("@/static/loginSignup/bg.png");
  background-color: #f8f8f8;
  background-size: 100%;
  background-repeat: no-repeat;
  padding-top: 232rpx;

  .logo {
    margin: 0 auto 80rpx;
    display: flex;
    gap: 11rpx;
    align-items: center;
    justify-content: center;
    width: 375rpx;

    .logo-icon {
      width: 64rpx;
      height: 64rpx;
      border-radius: 16rpx;
      background-color: #7413ff;
    }

    .logo-title {
      font-size: 64rpx;
      color: #1f1f1f;
      line-height: 90rpx;
      height: 90rpx;
      font-family: 阿里巴巴普惠体;
      font-weight: bold;
    }
  }

  .form {
    padding: 0 64rpx;

    .code {
      text-align: center;
      width: 142rpx;
      height: 48rpx;
      border-radius: 24rpx;
      padding: 0 16rpx;
      font-size: 22rpx;
      font-family: 苹方-简 中黑体;
      line-height: 48rpx;
      box-sizing: border-box;
      color: #ffffff;
    }

    // 密码确认状态样式
    .password-tip {
      font-size: 24rpx;
      margin-top: 8rpx;
      margin-left: 24rpx;

      &.error {
        color: #ff4757;
      }

      &.success {
        color: #2ed573;
      }
    }

    // 密码匹配图标样式
    .match-icon {
      width: 32rpx;
      height: 32rpx;
      border-radius: 50%;
      background-color: #2ed573;
      color: white;
      font-size: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }

    .mismatch-icon {
      width: 32rpx;
      height: 32rpx;
      border-radius: 50%;
      background-color: #ff4757;
      color: white;
      font-size: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }

    .login-btn {
      margin: 32rpx 0 40rpx;
      font-size: 26rpx;
      color: #7414ff;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .submit-btn {
      width: 622rpx;
      height: 88rpx;
      box-sizing: border-box;
      background-color: #c3c3c3;
      font-size: 32rpx;
      color: #ffffff;
      line-height: 88rpx;
      text-align: center;
      font-weight: bold;
      border-radius: 24rpx;
      font-family: HarmonyOS Sans SC Black;
    }
  }

  .other-login {
    position: absolute;
    bottom: 210rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 610rpx;
    display: flex;
    flex-direction: column;
    color: #b2b2b2;
    font-size: 26rpx;
    gap: 32rpx;
    align-items: center;
    justify-content: center;

    .other-title {
      position: relative;

      &::before {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: -148rpx;
        content: "";
        display: block;
        width: 106rpx;
        height: 0;
        border: 1rpx solid;
        border-image: linear-gradient(
            270deg,
            rgba(200, 200, 200, 1),
            rgba(151, 151, 151, 0)
          )
          1 1;
      }

      &::after {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -148rpx;
        content: "";
        display: block;
        width: 106rpx;
        height: 0;
        border: 1rpx solid;
        border-image: linear-gradient(
            -270deg,
            rgba(200, 200, 200, 1),
            rgba(151, 151, 151, 0)
          )
          1 1;
      }
    }
  }

  .agreement {
    color: #b2b2b2;
    font-size: 26rpx;
    text-align: center;
    position: absolute;
    width: 598rpx;
    left: 50%;
    transform: translateX(-50%);
    bottom: 32rpx;
  }
}

.color-7414FF {
  color: #7414ff;
}
</style>
