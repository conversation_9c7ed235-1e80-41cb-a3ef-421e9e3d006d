<template>
  <view class="home" :style="statusBarStyle">
    <view class="header">
      <view class="date">
        <view class="date-left">10</view>
        <view class="date-content">
          <view class="date-title">
            <view>今日宜穿</view>
            <FontBottomColor
              title="黄色"
              underline="#FFC900"
              color="#2F2F2F"
              size="38rpx"
            ></FontBottomColor>
          </view>
          <view class="date-time">农历三月初三</view>
        </view>
      </view>
      <view class="weather">
        <image
          style="width: 100rpx; height: 100rpx"
          src="@/static/home/<USER>"
        ></image>
        <view class="temperature">
          <view class="temperature-title">15°C</view>
          <view class="temperature-content">晴转多云</view>
        </view>
      </view>
    </view>
    <view class="fortune">
      <view class="left">
        <image src="@/static/icon/<EMAIL>"></image>
        <view class="content">运势详情</view>
      </view>
      <view class="right">
        <image src="@/static/icon/<EMAIL>"></image>
      </view>
    </view>
    <view class="chats">
      <view class="score-chart">
        <ScoreCharts></ScoreCharts>
      </view>
      <view class="exponent-chart">
        <ExponentCharts></ExponentCharts>
      </view>
    </view>
    <view class="tips">
      <view class="tips-title"> 彭祖百忌：己不破券 酉不会客</view>
      <view class="tips-content">
        <text class="color-423082 content" style="font-weight: bold"
          >【事业】</text
        >
        <text class="content">
          正处于创业、发展的阶段，各种困难很多。正处于创业、发展的阶段，各种困难很多。
        </text>
        <text class="color-423082 content">
          查看更多
          <image
            src="@/static/icon/next_icon.png"
            style="width: 30rpx; height: 30rpx; vertical-align: middle"
          ></image>
        </text>
      </view>
    </view>
    <view class="img"></view>
    <view class="category">
      <view
        v-for="(item, index) in categoryList"
        :key="index"
        class="category-item"
      >
        <image :src="item.icon" style="width: 80rpx; height: 80rpx"></image>
        <view class="category-title">{{ item.label }}</view>
      </view>
    </view>
    <view class="outfit">
      <view class="top">
        <view class="left">
          <image
            src="@/static/home/<USER>"
            style="width: 40rpx; height: 40rpx"
          ></image>
          <view>穿搭建议</view>
          <FontBottomColor
            title="黄色"
            underline="#FFC900"
            color="#000"
            size="32rpx"
          ></FontBottomColor>
        </view>
        <view class="right">
          <view>查看推荐穿搭</view>
          <image
            src="@/static/icon/next.png"
            style="width: 30rpx; height: 30rpx"
          ></image>
        </view>
      </view>
      <scroll-view class="banner" scroll-x="true" show-scrollbar="false">
        <view class="banner-container">
          <view
            class="banner-item"
            v-for="(item, index) in outfitList"
            :key="index"
          >
            <image :src="item.img" class="banner-image"></image>
            <view class="banner-info">
              <text class="banner-title">{{ item.title }}</text>
              <view class="banner-source">
                <view class="left">
                  <image :src="item.sourceIcon" class="source-icon"></image>
                  <text class="source-text">{{ item.source }}</text>
                </view>
                <image
                  src="/static/index/加入购物车icon.png"
                  style="width: 48rpx; height: 48rpx"
                ></image>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    <TalentList />

    <!-- 发布类型选择弹窗 -->
    <PublishTypeModal
      :visible="showPublishModal"
      @close="handlePublishModalClose"
      @select="handlePublishTypeSelect"
    />
  </view>
</template>

<script>
import Tab from "@/components/common/Tab.vue";
import statusBarMixin from "@/mixins/statusBarMixin.js";
import tabBarMixin from "@/mixins/tabBarMixin.js";
import ExponentCharts from "@/components/charts/ExponentCharts.vue";
import ScoreCharts from "@/components/charts/ScoreCharts.vue";
import FontBottomColor from "@/components/common/FontBottomColor.vue";
import TalentList from "@/pages/talentList/talentList.vue";
import PublishTypeModal from "@/components/common/PublishTypeModal.vue";

export default {
  mixins: [statusBarMixin, tabBarMixin],
  components: {
    TalentList,
    FontBottomColor,
    Tab,
    ExponentCharts,
    ScoreCharts,
    PublishTypeModal,
  },
  data() {
    return {
      tabList: [
        { title: "达人在线", tab: 1 },
        { title: "排行榜", tab: 2 },
        { title: "任务大厅", tab: 3 },
      ],
      categoryList: [
        { label: "问达人", icon: "/static/home/<USER>" },
        { label: "星座", icon: "/static/home/<USER>" },
        { label: "六爻", icon: "/static/home/<USER>" },
        { label: "紫薇", icon: "/static/home/<USER>" },
        { label: "达人", icon: "/static/home/<USER>" },
      ],
      outfitList: [
        {
          title:
            "去海边一定要穿黄色的衣服，这样拍照会更加好看，显得皮肤更白更有活力",
          source: "小红书",
          img: "/static/home/<USER>",
          sourceIcon: "/static/icon/xhs.png",
          icon: "/static/home/<USER>",
        },
        {
          title: "夏日穿搭指南：如何选择最适合自己的服装搭配方案",
          source: "小红书",
          img: "/static/home/<USER>",
          sourceIcon: "/static/icon/xhs.png",
          icon: "/static/home/<USER>",
        },
        {
          title: "去海边一定要穿“黄色”！",
          source: "小红书",
          img: "/static/home/<USER>",
          sourceIcon: "/static/icon/xhs.png",
          icon: "/static/home/<USER>",
        },
        {
          title: "去海边一定要穿“黄色”！",
          source: "小红书",
          img: "/static/home/<USER>",
          sourceIcon: "/static/icon/xhs.png",
          icon: "/static/home/<USER>",
        },
      ],
    };
  },
  onShow() {
    // 每次显示首页时可以在这里加载最新数据
    // 例如：更新天气信息、运势信息等
    console.log("首页显示，可以在这里加载最新数据");
  },
};
</script>

<style scoped lang="scss">
.home {
  background-image: url("@/static/home/<USER>");
  background-repeat: no-repeat;
  background-size: 100%;
  background-color: #f8f8f8;

  .header {
    display: flex;
    justify-content: space-between;
    padding: 66rpx 32rpx 0;

    .date {
      display: flex;
      align-items: center;

      .date-left {
        color: #000;
        font-size: 104rpx;
        line-height: 104rpx;
        font-weight: bold;
      }

      .date-content {
        display: flex;
        flex-direction: column;
        margin-left: 12rpx;

        .date-title {
          color: #000;
          font-size: 38rpx;
          font-weight: bold;
          display: flex;
        }

        .date-time {
          color: #8c8894;
          font-size: 28rpx;
        }
      }
    }

    .weather {
      display: flex;
      align-items: center;

      .temperature {
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 8rpx;

        .temperature-title {
          color: #2f2f2f;
          font-size: 40rpx;
          font-weight: bold;
        }

        .temperature-content {
          font-size: 24rpx;
          color: #8674a8;
        }
      }
    }
  }
  .fortune {
    margin: 16rpx 32rpx 0 32rpx;
    width: 686rpx;
    height: 68rpx;
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 16rpx;
    box-sizing: border-box;
    padding: 14rpx 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    image {
      width: 40rpx;
      height: 40rpx;
    }

    .left {
      display: flex;
      align-items: center;

      .content {
        margin-left: 8rpx;
        color: #848384;
        font-size: 28rpx;
      }
    }

    .right {
      display: flex;
      align-items: center;
    }
  }
  .chats {
    display: flex;
    justify-content: space-around;
    margin-top: 30rpx;
    padding: 0 32rpx;

    .score-chart {
      width: 215rpx;
      height: 200rpx;
    }

    .exponent-chart {
      width: 380rpx;
      height: 200rpx;
    }
  }
  .tips {
    padding: 0 32rpx;
    margin-top: 36rpx;
    .tips-title {
      color: #938da5;
      font-size: 26rpx;
    }
    .tips-content {
      position: relative;
      height: 112rpx;
      width: 686rpx;
      margin-top: 40rpx;
      font-size: 26rpx;
      color: #7f73a9;
      // 渐变背景颜色
      background: linear-gradient(to right, #ebefff, #e5dcff);
      border-radius: 24rpx;
      padding: 18rpx 24rpx;
      box-sizing: border-box;
      &::after {
        content: "";
        position: absolute;
        top: -20rpx;
        left: 100rpx;
        width: 0;
        height: 0;
        border-left: 15rpx solid transparent;
        border-right: 15rpx solid transparent;
        border-bottom: 20rpx solid #ebefff;
      }
      .content {
        line-height: 40rpx;
      }
    }
  }
  .img {
    margin: 0 32rpx;
    width: 686rpx;
    height: 372rpx;
    margin-top: 24rpx;
    background-image: url("@/static/home/<USER>");
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .category {
    height: 180rpx;
    margin: 24rpx 32rpx 0 32rpx;
    padding: 24rpx 47rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 24rpx;
    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 4rpx;
      .category-title {
        font-size: 24rpx;
      }
    }
  }
  .outfit {
    margin-top: 44rpx;
    .top {
      display: flex;
      padding: 0 32rpx;
      justify-content: space-between;
      align-items: center;
      .left {
        display: flex;
        align-items: center;
        gap: 8rpx;
        color: #000;
        font-size: 32rpx;
      }
      .right {
        display: flex;
        align-items: center;
        gap: 4rpx;
        color: #a79fb4;
        font-size: 26rpx;
      }
    }
    .banner {
      margin-top: 20rpx;
      white-space: nowrap;
      //margin-bottom: 40rpx;

      .banner-container {
        display: inline-flex;
        align-items: center;
        padding: 0 32rpx;
        gap: 16rpx;
      }

      .banner-item {
        width: 280rpx;
        min-height: 426rpx;
        flex-shrink: 0;
        border-radius: 24rpx;
        display: flex;
        flex-direction: column;

        .banner-image {
          width: 280rpx;
          height: 280rpx;
        }

        .banner-info {
          width: 283rpx;
          min-height: 180rpx;
          box-sizing: border-box;
          padding: 12rpx 16rpx 24rpx 16rpx;
          background-color: #fff;
          border-radius: 0 0 24rpx 24rpx;
          display: flex;
          flex-direction: column;
          gap: 12rpx;
          justify-content: space-between;

          .banner-title {
            font-size: 26rpx;
            color: #000;
            line-height: 36rpx;
            max-height: 72rpx;
            overflow: hidden; /* 超出隐藏 */
            text-overflow: ellipsis; /* 省略号 */
            display: -webkit-box; /* 弹性盒模型 */
            -webkit-box-orient: vertical; /* 垂直排列 */
            -webkit-line-clamp: 2; /* 限制2行 */
            font-weight: bold;
            word-break: break-all; /* 长文本拆分 */
            flex: 1;
            white-space: normal; /* 允许换行 */
          }

          .banner-source {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 50rpx;
            .left {
              display: flex;
              align-items: center;
              border-radius: 18rpx;
              background-color: #f4f4f4;
              padding: 4rpx;
              .source-icon {
                width: 32rpx;
                height: 32rpx;
                margin-right: 8rpx;
                border-radius: 4rpx;
              }

              .source-text {
                font-size: 24rpx;
                margin-right: 8rpx;
                color: #000;
              }
            }
          }
        }
      }
    }
  }
}
.color-423082 {
  color: #423082;
}
</style>
