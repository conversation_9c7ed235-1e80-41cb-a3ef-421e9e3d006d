<template>
  <view class="talent" :style="statusBarStyle">
    <Tab
      :tabList="tabList"
      :defaultTab="currentTab"
      textColor="#999999"
      activeColor="#8147FF"
      justifyContent="start"
      @tab-change="handleTabChange"
      minWidth="140rpx"
    ></Tab>
    <view class="list-top">
      <view class="sort">综合排序</view>
      <view class="screening">
        <view class="screening-content">
          达人等级
          <image class="down-icon" src="@/static/icon/dropdown.png"></image>
        </view>
        <view class="screening-content">
          达人等级
          <image class="down-icon" src="@/static/icon/dropdown.png"></image>
        </view>
      </view>
    </view>
    <view class="talent-list">
      <view class="talent-item popular">
        <view class="popular-title">2人排队中</view>
        <view class="popular-avatar">
          <Avatar color="#fff"></Avatar>
        </view>
      </view>
      <view v-for="(item, index) in 9" :key="index" class="talent-item">
        <view class="top">
          <view class="tag top-item">国学</view>
          <view class="people top-item">
            <image class="people-icon" src="@/static/icon/people.png"></image>
            11
          </view>
        </view>
        <Avatar color="#7e47ff"></Avatar>
        <view class="bottom">
          <view class="title">感情密码,一切都是为了爱</view>
          <view class="subheading">
            <text>薄荷清甜</text>
            <view class="label">
              <view class="label-item color-8148ff">Lv3</view>
              <view class="label-item color-fdedbd">自荐</view>
            </view>
          </view>
          <view class="figures">
            <view class="figures-item">
              <view class="title">好评率</view>
              <view>100%</view>
            </view>
            <view class="figures-item">
              <view class="title">最近咨询</view>
              <view>999+</view>
            </view>
          </view>
          <view class="features">上周36个用户复购</view>
        </view>
      </view>
    </view>

    <!-- 发布类型选择弹窗 -->
    <PublishTypeModal
      :visible="showPublishModal"
      @close="handlePublishModalClose"
      @select="handlePublishTypeSelect"
    />
  </view>
</template>

<script>
import Tab from "@/components/common/Tab.vue";
import Avatar from "@/components/common/Avatar.vue";
import statusBarMixin from "@/mixins/statusBarMixin.js";
import tabBarMixin from "@/mixins/tabBarMixin.js";
import PublishTypeModal from "@/components/common/PublishTypeModal.vue";

export default {
  mixins: [statusBarMixin, tabBarMixin],
  components: { Avatar, Tab, PublishTypeModal },
  data() {
    return {
      currentTab: 1,
      tabList: [
        { title: "在线畅聊", tab: 1 },
        { title: "任务大厅", tab: 2 },
        { title: "交流论坛", tab: 3 },
      ],
      screenOptions: [
        { label: "达人登记", value: 1 },
        { label: "达人登记", value: 2 },
      ],
    };
  },
  onShow() {
    // 每次显示页面时重新加载当前tab的数据
    this.loadDataByTab(this.currentTab);
  },
  methods: {
    switchTab(tab) {
      this.currentTab = tab;
    },
    handleTabChange(data) {
      this.currentTab = data.tab;
      console.log("Tab切换到:", data.item.title, "标签值:", data.tab);
      // 这里可以根据不同的tab值加载不同的数据
      this.loadDataByTab(data.tab);
    },
    loadDataByTab(tab) {
      // 根据tab值加载对应的数据
      switch (tab) {
        case 1:
          console.log("加载在线畅聊数据");
          break;
        case 2:
          console.log("加载任务大厅数据");
          break;
        case 3:
          console.log("加载交流论坛数据");
          break;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.talent {
  padding: 32rpx;
  background-color: #f5f5f5;

  .list-top {
    display: flex;
    justify-content: space-between;
    color: #6a686e;

    .sort {
      flex: 1;
    }

    .screening {
      display: flex;
      gap: 36rpx;

      .screening-content {
        display: flex;
        align-items: center;
      }

      .down-icon {
        width: 28rpx;
        height: 28rpx;
      }
    }
  }

  .talent-list {
    margin-top: 24rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .talent-item {
      width: 335rpx;
      height: 412rpx;
      border-radius: 24rpx;
      padding: 24rpx;
      box-sizing: border-box;
      background: linear-gradient(180deg, #f4f0ff 0%, #ffffff 100%);

      .top {
        display: flex;
        justify-content: space-between;

        .top-item {
          border-radius: 24rpx;
          padding: 4rpx 16rpx;
          font-size: 20rpx;
          line-height: 28rpx;
        }

        .tag {
          background-color: #6754ff;
          color: #fff;
        }

        .people {
          display: flex;
          align-items: center;
          background-color: #fff;
          color: #c2baca;

          .people-icon {
            width: 20rpx;
            height: 20rpx;
            margin-right: 5rpx;
          }
        }
      }

      .bottom {
        display: flex;
        flex-direction: column;
        gap: 16rpx;

        .title {
          font-size: 32rpx;
          color: #000;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          font-weight: bold;
        }

        .subheading {
          color: #5a5765;
          font-size: 32rpx;
          display: flex;
          gap: 16rpx;
          align-items: center;

          .label {
            display: flex;
            flex: 1;
            align-items: center;
            font-size: 22rpx;
            gap: 4rpx;

            .label-item {
              border-radius: 8rpx;
              height: 30rpx;
              line-height: 30rpx;
              padding: 0 8rpx;
            }
          }
        }

        .figures {
          line-height: 22rpx;
          height: 22rpx;
          color: #000;
          display: flex;
          justify-content: space-between;

          .figures-item {
            display: flex;
            align-items: center;
            font-size: 22rpx;
            line-height: 22rpx;
            gap: 8rpx;

            .title {
              color: #989898;
              font-size: 22rpx;
              line-height: 22rpx;
            }
          }
        }

        .features {
          color: #ed9f2f;
          line-height: 22rpx;
          font-size: 22rpx;
        }
      }
    }

    .popular {
      background-image: url("@/static/home/<USER>");
      background-size: 100%;
      position: relative;

      .popular-title {
        position: absolute;
        top: 68rpx;
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.6);
      }

      .popular-avatar {
        position: absolute;
        top: 190rpx;
        left: 50%;
        transform: translateX(-50%) translateY(-50%);
      }
    }
  }
}

.color-8148ff {
  background-color: #8147ff;
  color: #fff;
}

.color-fdedbd {
  background-color: #fdedbd;
  color: #cc740f;
}
</style>
