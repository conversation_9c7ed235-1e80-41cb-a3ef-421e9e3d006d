<template>
	<view class="page">
		<!-- 搜索板块 -->
		<view class="page-title">
			<image src="https://boshi.channce.com/imgs/yxstatic/logo.png" mode="widthFix"></image>
			<uni-search-bar @focus="goPage('/pages/product/search')" :focus="false" cancelButton="none" placeholder="输入关键词搜索"></uni-search-bar>
		</view>
		<view class="content">
			<scroll-view scroll-y class="left-aside">
				<view v-for="item in flist" :key="item.id" class="f-item" :class="{active: item.id === currentId}" @click="tabtap(item)">
					{{ item.classifyName }}
				</view>
			</scroll-view>
			<scroll-view scroll-with-animation scroll-y class="right-aside" @scroll="asideScroll" :scroll-top="tabScrollTop">
				<view class="goodList">
					<view class="goodList-item" v-for="(item, index) in goodsList" :key="index" @click="navToList(item.id, index)">
						<image :src="item.showPic | jointPic" mode="aspectFill"></image>
						<view class="right">
							<view class="top">{{ item.name }}</view>
							<view class="bottom">
								<view class="money" v-if="item.plural != 2">￥{{ item.money }}</view>
								<view class="money" v-if="item.plural == 2">{{ item.money }}积分</view>
								<!-- <view class="btn">月销1万</view> -->
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				sizeCalcState: false,
				tabScrollTop: 0,
				currentId: 1,
				flist: [],
				slist: [],
				goodsList: [],
			}
		},
		onLoad() {
			this.loadData();
		},
		methods: {
			async loadData() {
				this.http({
					url: '/api/cc/ccL',
					method: 'post',
					data: { pluralId: 1, classifyPid: 0 },
					success: res => {
						this.flist = res.data;
						this.currentId = res.data[0].id
						this.getDate();
					}
				});
			},
			getDate() {
				this.http({
					url: '/api/ssc/scList',
					method: 'post',
					data: {
						commodityClassifyLevel1: this.currentId,
						commodityClassifyLevel2: 0,
						limit: 100,
						offset: 1,
						plural: 1,
						shareName: '',
						userId: uni.getStorageSync('userInfo').userId
					},
					success: res => {
						res.data.forEach(item => {
							this.goodsList.push(item);
						});
					}
				});
			},
			//一级分类点击
			tabtap(item) {
				this.currentId = item.id;
				this.goodsList = []
				this.getDate()
			},
			//右侧栏滚动
			asideScroll(e) {
				if (!this.sizeCalcState) {
					this.calcSize();
				}
				let scrollTop = e.detail.scrollTop;
				let tabs = this.slist.filter(item => item.top <= scrollTop).reverse();
				if (tabs.length > 0) {
					this.currentId = tabs[0].pid;
				}
			},
			//计算右侧栏每个tab的高度等信息
			calcSize() {
				let h = 0;
				this.slist.forEach(item => {
					let view = uni.createSelectorQuery().select("#main-" + item.id);
					view.fields({
						size: true
					}, data => {
						item.top = h;
						h += data.height;
						item.bottom = h;
					}).exec();
				})
				this.sizeCalcState = true;
			},
			navToList(goodsId, tid) {
				uni.navigateTo({ url: `/pages/product/product?goodsId=${goodsId}&type=1&stutas=1` });
			},
			goPage(url) {
				uni.navigateTo({ url });
			},
		}
	}
</script>

<style lang='scss'>
	.page {
		background-image: url('https://boshi.channce.com/imgs/yxstatic/bg/background.png');
		background-size: 100%;
		background-repeat: no-repeat;
		padding-top: 60rpx;

		.page-title {
			height: 140rpx;
			width: 95vw;
			margin: 0 auto;
			display: flex;
			align-items: center;
			justify-content: space-between;

			image {
				width: 35%;
			}

			::v-deep .uni-searchbar {
				width: 60%;

				.uni-searchbar__box {
					border-radius: 100rpx !important;
				}
			}
		}
	}

	.content {
		display: flex;
		height: calc(100vh - 200rpx);
		background-color: #ffffff;
		border-radius: 10px 10px 0 0;
		overflow: hidden;

		.left-aside {
			flex-shrink: 0;
			width: 200upx;
			height: 100%;
			background: #f5f5f5;

			.f-item {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100upx;
				font-size: 28upx;
				color: $font-color-base;
				position: relative;

				&.active {
					color: $base-color;
					background-color: #fff;
					border-radius: 10px 0 0 0;

					&:before {
						content: '';
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
						height: 36upx;
						width: 8upx;
						background-color: $base-color;
						border-radius: 0 4px 4px 0;
						opacity: .8;
					}

					&:after {
						content: '';
						position: absolute;
						right: 0;
						bottom: -15px;
						width: 15px;
						height: 15px;
						line-height: 100px;
						z-index: 99;
						display: block;
						text-align: center;
						background-image: radial-gradient(200px at 15px 0px, transparent 15px, #fff 15px);
						transform: rotate(180deg);
					}

				}
			}
		}


		.right-aside {
			flex: 1;
			overflow: hidden;
			height: 100%;
			background: #fff;

			.goodList {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;
				padding: 20rpx 15rpx;

				.goodList-item {
					width: 48%;
					border-radius: 10px 10px 10px 10px;
					background-color: #ffffff;
					color: #000000;
					font-size: 14px;
					margin-bottom: 40upx;
					box-shadow: 0px 0px 5px -1px #a7a7a7;

					image {
						width: 100%;
						height: 240rpx;
						border-radius: 10px 10px 0px 0px;
					}

					.right {
						width: 100%;
						padding: 10rpx 20rpx;

						.top {
							font-size: 26rpx;
							display: -webkit-box;
							overflow: hidden;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
						}

						.top-box {
							display: inline-block;
							font-size: 12px;
							color: #af9029;
							border: 1px solid #af9029;
							border-radius: 5rpx;
							padding: 0 10rpx;
						}

						.bottom {
							display: flex;
							width: 100%;
							align-items: flex-end;
							padding-top: 5rpx;
							justify-content: flex-end;

							.money {
								color: rgba(236, 65, 19, 1);
								font-size: 26rpx;
							}

							.btn {
								color: rgba(171, 168, 168, 0.88);
								font-size: 24rpx;
								text-decoration: line-through;
								margin-left: 2px;
							}
						}
					}
				}
			}
		}
	}
</style>