<template>
  <view class="mine">
    <view class="header">
      <image src="/static/icon/share_icon.png"></image>
      <image src="/static/icon/setting_icon.png"></image>
    </view>
    <view class="avatar">
      <image
        src="/static/home/<USER>"
        alt=""
        style="
          width: 120rpx;
          height: 120rpx;
          border-radius: 50%;
          border: 4rpx solid #ffffff;
        "
        mode="aspectFill"
      ></image>
      <view class="name">
        <view class="name-content">用户昵称</view>
        <view class="name-id">ID: 123456</view>
      </view>
      <view class="edit"> 编辑资料 </view>
    </view>
    <view class="signature">人分两类，迷人乏味</view>
    <view class="tag">
      <view class="tag-item">
        <image
          src="/static/icon/man_icon.png"
          style="width: 24rpx; height: 24rpx"
        ></image>
        <view>男</view>
      </view>
      <view class="tag-item">
        <view>中国</view>
      </view>
    </view>
    <view class="data">
      <view class="num">
        <view class="num-item">
          <view class="num-title">500</view>
          <view class="num-content">关注</view>
        </view>
        <view class="num-item">
          <view class="num-title">1958</view>
          <view class="num-content">粉丝</view>
        </view>
        <view class="num-item">
          <view class="num-title">73968</view>
          <view class="num-content">获赞与收藏</view>
        </view>
      </view>
      <view class="assets">
        <view class="top">
          <view class="item">
            <image src="/static/mine/diamond.png"></image>
            <view class="item-content">
              <view class="item-title">钻石</view>
              <view class="item-content">430.40</view>
            </view>
          </view>
          <view class="item">
            <image src="/static/mine/coupons.png"></image>
            <view class="item-content">
              <view class="item-title">优惠券</view>
              <view class="item-content">13张</view>
            </view>
          </view>
          <view class="item">
            <image src="/static/mine/order.png"></image>
            <view class="item-content">
              <view class="item-title">订单</view>
            </view>
          </view>
        </view>
        <view class="loser">成为达人（审核中）</view>
      </view>
    </view>
    <view class="content">
      <Tab
        style="padding: 0 88rpx"
        :tabList="tabList"
        textColor="#878787"
        activeColor="#7414FF"
        justifyContent="space-between"
        minWidth="100rpx"
      ></Tab>
      <view class="list">
        <image
          @click="navTo('/pages/publish/publishType')"
          src="/static/mine/go_post.png"
          style="
            width: 180rpx;
            height: 262rpx;
            margin: 84rpx auto;
            display: block;
          "
        ></image>
      </view>
    </view>
  </view>
</template>

<script>
import Tab from "@/components/common/Tab.vue";
import tabBarMixin from "@/mixins/tabBarMixin";
import { navTo } from "@/utils/navGetTo";

export default {
  mixins: [tabBarMixin],
  components: { Tab },
  data() {
    return {
      tabList: [
        { title: "问答", tab: 1 },
        { title: "动态", tab: 2 },
        { title: "收藏", tab: 3 },
        { title: "喜欢", tab: 4 },
      ],
    };
  },
  onShow() {
    // 每次显示我的页面时可以在这里加载用户信息
    // 例如：更新用户资料、积分、等级等信息
    console.log("我的页面显示，可以在这里加载用户最新信息");
  },
  methods: { navTo },
};
</script>

<style scoped lang="scss">
.mine {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(to bottom, #8b3bff 0%, #b581ff 100%);

  .header {
    padding: 0 32rpx;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 40rpx;
    image {
      width: 48rpx;
      height: 48rpx;
    }
  }
  .avatar {
    height: 120rpx;
    padding-left: 32rpx;
    padding-right: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 48rpx;
    .name {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 12rpx;
      color: #fff;
      flex: 1;
      margin-left: 40rpx;
      font-family:
        PingFangSC,
        PingFang SC;
      .name-content {
        font-weight: 600;
        font-size: 44rpx;
        line-height: 56rpx;
      }
      .name-id {
        font-weight: 400;
        font-size: 26rpx;
        line-height: 36rpx;
      }
    }
    .edit {
      border-radius: 22rpx;
      border: 1rpx solid #ffffff;
      padding: 6rpx 20rpx;
      color: #fff;
      font-size: 24rpx;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      line-height: 32rpx;
      background-color: rgba(255, 255, 255, 0.32);
    }
  }
  .signature {
    padding: 0 32rpx;
    margin-top: 32rpx;
    font-size: 26rpx;
    color: #fff;
    line-height: 37rpx;
  }
  .tag {
    padding: 0 32rpx;
    display: flex;
    gap: 16rpx;
    margin-top: 22rpx;
    .tag-item {
      display: flex;
      align-items: center;
      border-radius: 20rpx;
      box-sizing: border-box;
      height: 40rpx;
      gap: 4rpx;
      background-color: rgba(255, 255, 255, 0.4);
      padding: 8rpx 16rpx;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 26rpx;
      color: #ffffff;
      line-height: 26rpx;
    }
  }

  .data {
    padding: 0 32rpx;
    font-size: 28rpx;
    color: #666;
    .num {
      display: flex;
      gap: 53rpx;
      margin-top: 40rpx;

      .num-item {
        width: 188rpx;
        height: 108rpx;
        color: #fff;
        display: flex;
        align-items: center;
        flex-direction: column;
        .num-title {
          font-family: DINCond, DINCond;
          font-weight: 900;
          font-size: 64rpx;
          color: #ffffff;
          line-height: 76rpx;
        }
        .num-content {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #ffffff;
          line-height: 32rpx;
        }
      }
    }
    .assets {
      margin-top: 24rpx;
      padding-bottom: 24rpx;
      display: flex;
      flex-direction: column;
      gap: 12rpx;
      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 12rpx;
        .item {
          color: #fff;
          font-size: 28rpx;
          background-color: #9554f2;
          border-radius: 12rpx;
          width: 220rpx;
          align-items: center;
          height: 110rpx;
          padding: 15rpx 20rpx;
          box-sizing: border-box;
          display: flex;
          gap: 16rpx;
          image {
            width: 64rpx;
            height: 64rpx;
          }
          .item-title {
            color: rgba(255, 255, 255, 0.72);
            font-size: 28rpx;
            line-height: 40rpx;
          }
          .item-content {
            line-height: 40rpx;
            font-weight: 600;
          }
        }
      }
      .loser {
        width: 686rpx;
        height: 72rpx;
        line-height: 72rpx;
        background-color: #9554f2;
        border-radius: 12rpx;
        color: rgba(255, 255, 255, 0.72);
        text-align: center;
      }
    }
  }
  .content {
    flex: 1;
    background: linear-gradient(180deg, #f1e8ff 0%, #f8f8f8 100%);
    border-radius: 32rpx 32rpx 0 0;
    display: flex;
    flex-direction: column;
    .list {
      flex: 1;
      overflow: scroll;
    }
  }
}
</style>
