<template>
  <view class="content">
    <view class="navbar">
      <view
        v-for="(item, index) in navList"
        :key="index"
        class="nav-item"
        :class="{ current: tabCurrentIndex == item.state }"
        @click="tabClick(item.state)"
        >{{ item.text }}</view
      >
    </view>

    <scroll-view
      class="list-scroll-content"
      scroll-y
      @scrolltolower="scrollLower"
    >
      <!-- 空白页 -->
      <noMore v-if="orderList.length === 0"></noMore>
      <!-- 订单列表 -->
      <view v-for="(item, index) in orderList" :key="index" class="order-item">
        <view class="i-top b-b">
          <text class="time" v-if="item.state == 3">{{
            item.selfOrder.tradeTime | formatDate
          }}</text>
          <text class="time" v-else>{{
            item.selfOrder.tradeTime | formatDate
          }}</text>
          <text class="state" :style="{ color: item.stateTipColor }">{{
            item.pluralName
          }}</text>
        </view>
        <view class="goods-box-single">
          <image
            class="goods-img"
            :src="item.sharesShowPic | jointPic"
            mode="widthFix"
          ></image>
          <view class="right">
            <view class="right-top">
              <text class="title clamp">{{
                item.selfOrder.tradeGoodsName
              }}</text>
            </view>
            <text class="price" v-if="item.selfOrder.plural == 1">{{
              item.selfOrder.tradeMoney
            }}</text>
            <text class="price1" v-if="item.selfOrder.plural == 2">{{
              item.selfOrder.tradeMoney
            }}</text>
            <view class="spec">
              <text v-if="item.selfOrder.model"
                >{{ item.selfOrder.model }},</text
              >
              <text v-if="item.selfOrder.standard"
                >{{ item.selfOrder.standard }},</text
              >
              <text v-if="item.selfOrder.sizes">{{
                item.selfOrder.sizes
              }}</text>
            </view>
            <view class="orderId"
              >订单号：{{ item.selfOrder.orderTradeId }}</view
            >
            <view class="orderId" v-if="item.selfOrder.expressOrder"
              >快递单号：{{ item.selfOrder.expressOrder }}</view
            >
          </view>
        </view>

        <view class="price-box">
          <view>
            <view class="state" v-if="tabCurrentIndex == 0">{{
              navList[item.selfOrder.tradeStatus].text
            }}</view>
          </view>
          <view class="">
            共
            <text class="num">{{ item.selfOrder.tradeNumber }}</text>
            件商品 实付款
            <text class="price" v-if="item.selfOrder.plural == 1">{{
              item.selfOrder.tradeMoney
            }}</text>
            <text class="price1" v-if="item.selfOrder.plural == 2">{{
              item.selfOrder.tradeMoney
            }}</text>
          </view>
        </view>
        <view class="action-box b-t">
          <button
            class="action-btn recom"
            v-if="item.selfOrder.tradeStatus == 0"
            @click="confirmPay(item.selfOrder.orderTradeId)"
          >
            去支付
          </button>
          <button
            class="action-btn recom"
            v-if="item.selfOrder.tradeStatus == 2"
            @click="confirmOrder(item.selfOrder.orderTradeId)"
          >
            确认收货
          </button>
          <button
            class="action-btn recom"
            v-if="item.selfOrder.tradeStatus == 2"
            @click="cheakLogistics(item.selfOrder.expressOrder)"
          >
            查看物流
          </button>
          <button
            class="action-btn refund"
            v-if="item.selfOrder.tradeStatus < 3"
            @click="refund(item.selfOrder)"
          >
            申请退款
          </button>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import uniLoadMore from "@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue";
import noMore from "@/components/noMore.vue";
export default {
  components: { uniLoadMore, noMore },
  data() {
    return {
      userId: uni.getStorageSync("userInfo").userId,
      allTotal: 0,
      tabCurrentIndex: 0,
      navList: [
        { state: -1, text: "全部订单", loadingType: "more", orderList: [] },
        { state: 1, text: "待发货", loadingType: "more", orderList: [] },
        { state: 2, text: "待收货", loadingType: "more", orderList: [] },
        { state: 4, text: "已完成", loadingType: "more", orderList: [] },
        { state: 3, text: "已取消", loadingType: "more", orderList: [] },
      ],
      orderList: [],
      page: 1,
    };
  },

  onLoad(options) {
    this.tabCurrentIndex = +options.state;
    this.loadData();
  },
  methods: {
    //获取订单列表
    loadData(source) {
      this.http({
        data: {
          a_m: "so_soList",
          a_d: this.$rsa.encrypt(
            {
              limit: 10,
              offset: this.page,
              tradeStatus: this.tabCurrentIndex,
              userId: this.userId,
            },
            true,
          ),
        },
        success: (res) => {
          this.allTotal = res.data.total;
          for (let i = 0; i < res.data.length; i++) {
            this.orderList.push(res.data[i]);
          }
        },
      });
    },
    scrollLower() {
      if (this.orderList.length >= this.allTotal) {
        this.status = "noMore";
        this.$api.msg("当前已加载完毕");
        return;
      } else {
        this.status = "loading";
        this.page++;
        this.loadData();
      }
    },
    //顶部tab点击
    tabClick(index) {
      console.log(index);
      this.tabCurrentIndex = index;
      this.page = 1;
      this.orderList = [];

      // 更新路由参数，保持URL和当前状态一致
      // 使用 H5 的 history.replaceState 来更新URL参数，不会重新加载页面
      // #ifdef H5
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0]; // 获取不带参数的URL
      const newUrl = baseUrl + '?state=' + index;
      window.history.replaceState(null, '', newUrl);
      // #endif

      // 加载对应tab的数据
      this.loadData();
    },
    confirmOrder(orderTradeId) {
      this.http({
        data: {
          a_m: "so_confirmReceipt",
          a_d: this.$rsa.encrypt(
            { orderNo: orderTradeId, userId: this.userId },
            true,
          ),
        },
        success: (res) => {
          this.$api.msg(res.message);
          this.loadData();
        },
      });
    },
    confirmPay(orderTradeId) {
      this.http({
        data: {
          a_m: "so_affirmPay",
          a_d: this.$rsa.encrypt(
            { orderNo: orderTradeId, payWay: 10, userId: this.userId },
            true,
          ),
        },
        success: (res) => {
          this.$api.msg(res.message);
          if (res.code == 2000) {
            if (res.data.ra_Code == 100) {
              var reg =
                /(https?|http|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/g;
              var urlStr = encodeURI(res.data.rc_Result.match(reg));
              plus.runtime.openURL(urlStr);
              setTimeout(() => {
                uni.redirectTo({
                  url: `/pages/money/paySuccess`,
                });
              }, 2000);
            } else {
              this.$api.msg(res.data.rb_CodeMsg);
            }
          }
        },
        fail: (err) => {
          this.$api.msg(res.message);
        },
      });
    },
    refund(item) {
      console.log(item);
      let that = this;
      uni.showModal({
        title: "退款理由",
        editable: true, // 启用输入框
        placeholderText: "请输入退款理由",
        success: function (res) {
          if (res.confirm) {
            that.http({
              data: {
                a_d: that.$rsa.encrypt(
                  {
                    orderNo: item.orderTradeId,
                    reason: res.content,
                    userId: that.userId,
                  },
                  true,
                ),
                a_m: "so_applyRefund",
              },
              success: (res) => {
                that.$api.msg(res.message);
                setTimeout(() => {
                  that.orderList = [];
                  that.page = 1;
                  that.loadData();
                }, 3000);
              },
            });
          }
        },
      });
    },
    cheakLogistics(waybill) {
      plus.runtime.openURL(
        `https://page.cainiao.com/guoguo/app-myexpress-taobao/express-detail.html?mailNo=${waybill}`,
      );
    },
  },
};
</script>

<style lang="scss">
page,
.content {
  background: $page-color-base;
  height: 100%;
}

.swiper-box {
  height: calc(100% - 40px);
}

.list-scroll-content {
  height: 100%;
}

.navbar {
  display: flex;
  height: 40px;
  padding: 0 5px;
  background: #fff;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;

  .nav-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 15px;
    color: $font-color-dark;
    position: relative;

    &.current {
      color: $base-color;

      &:after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 44px;
        height: 0;
        border-bottom: 2px solid $base-color;
      }
    }
  }
}

.uni-swiper-item {
  height: auto;
}

.order-item {
  display: flex;
  flex-direction: column;
  padding-left: 30upx;
  background: #fff;
  margin-top: 16upx;

  .i-top {
    display: flex;
    align-items: center;
    height: 80upx;
    padding-right: 30upx;
    font-size: $font-base;
    color: $font-color-dark;
    position: relative;

    .time {
      flex: 1;
    }

    .state {
      color: $base-color;
    }

    .del-btn {
      padding: 10upx 0 10upx 36upx;
      font-size: $font-lg;
      color: $font-color-light;
      position: relative;

      &:after {
        content: "";
        width: 0;
        height: 30upx;
        border-left: 1px solid $border-color-dark;
        position: absolute;
        left: 20upx;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }

  /* 多条商品 */
  .goods-box {
    height: 160upx;
    padding: 20upx 0;
    white-space: nowrap;

    .goods-item {
      width: 120upx;
      height: 120upx;
      display: inline-block;
      margin-right: 24upx;
    }

    .goods-img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  /* 单条商品 */
  .goods-box-single {
    display: flex;
    padding: 20upx 0;

    .goods-img {
      display: block;
      width: 170upx;
      height: 170upx;
    }

    .right {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 0 30upx 0 24upx;
      overflow: hidden;

      .right-top {
        display: flex;
        padding-bottom: 10rpx;

        .title {
          font-size: $font-base + 2upx;
          color: $font-color-dark;
          line-height: 1;
          max-width: 50%;
          // overflow: hidden;
        }

        .attr-box {
          font-size: $font-sm + 2upx;
          color: $font-color-light;
          padding: 0 12upx;
        }
      }

      .spec {
        padding-bottom: 20rpx;
        padding-top: 20rpx;

        text {
          color: $font-color-light;
        }
      }

      .orderId {
        font-size: $font-sm + 2upx;
        color: $font-color-light;
        padding: 0rpx 0 12upx;
      }

      .price {
        font-size: $font-base + 2upx;
        color: $font-color-dark;

        &:before {
          content: "￥";
          font-size: $font-sm;
          margin: 0 2upx 0 8upx;
        }
      }

      .price1 {
        font-size: $font-base + 2upx;
        color: $font-color-dark;

        &::after {
          content: "积分";
          font-size: $font-sm;
          margin: 0 2upx 0 8upx;
        }
      }
    }
  }

  .price-box {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    padding: 20upx 30upx;
    font-size: $font-sm + 2upx;
    color: $font-color-light;
    padding-left: 0;

    .state {
      background-color: #f7bcc8;
      color: #fff;
      padding: 2px 10px;
    }

    .num {
      margin: 0 8upx;
      color: $font-color-dark;
    }

    .price {
      font-size: $font-lg;
      color: $font-color-dark;

      &:before {
        content: "￥";
        font-size: $font-sm;
        margin: 0 2upx 0 8upx;
      }
    }

    .price1 {
      font-size: $font-lg;
      color: $font-color-dark;
      margin-left: 10rpx;

      &::after {
        content: "积分";
        font-size: $font-sm;
        margin: 0 2upx 0 8upx;
      }
    }
  }

  .action-box {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: relative;
    padding-right: 30upx;
  }

  .action-btn {
    width: 160upx;
    height: 60upx;
    margin: 20rpx;
    margin-left: 24upx;
    text-align: center;
    line-height: 60upx;
    font-size: $font-sm + 2upx;
    color: $font-color-dark;
    background: #fff;
    border-radius: 100px;

    &:after {
      border-radius: 100px;
    }

    &.recom {
      background: #fff9f9;
      color: $base-color;

      &:after {
        border-color: #f7bcc8;
      }
    }
  }
}

/* load-more */
.uni-load-more {
  display: flex;
  flex-direction: row;
  height: 80upx;
  align-items: center;
  justify-content: center;
}

.uni-load-more__text {
  font-size: 28upx;
  color: #999;
}

.uni-load-more__img {
  height: 24px;
  width: 24px;
  margin-right: 10px;
}

.uni-load-more__img > view {
  position: absolute;
}

.uni-load-more__img > view view {
  width: 6px;
  height: 2px;
  border-top-left-radius: 1px;
  border-bottom-left-radius: 1px;
  background: #999;
  position: absolute;
  opacity: 0.2;
  transform-origin: 50%;
  animation: load 1.56s ease infinite;
}

.uni-load-more__img > view view:nth-child(1) {
  transform: rotate(90deg);
  top: 2px;
  left: 9px;
}

.uni-load-more__img > view view:nth-child(2) {
  transform: rotate(180deg);
  top: 11px;
  right: 0;
}

.uni-load-more__img > view view:nth-child(3) {
  transform: rotate(270deg);
  bottom: 2px;
  left: 9px;
}

.uni-load-more__img > view view:nth-child(4) {
  top: 11px;
  left: 0;
}

.load1,
.load2,
.load3 {
  height: 24px;
  width: 24px;
}

.load2 {
  transform: rotate(30deg);
}

.load3 {
  transform: rotate(60deg);
}

.load1 view:nth-child(1) {
  animation-delay: 0s;
}

.load2 view:nth-child(1) {
  animation-delay: 0.13s;
}

.load3 view:nth-child(1) {
  animation-delay: 0.26s;
}

.load1 view:nth-child(2) {
  animation-delay: 0.39s;
}

.load2 view:nth-child(2) {
  animation-delay: 0.52s;
}

.load3 view:nth-child(2) {
  animation-delay: 0.65s;
}

.load1 view:nth-child(3) {
  animation-delay: 0.78s;
}

.load2 view:nth-child(3) {
  animation-delay: 0.91s;
}

.load3 view:nth-child(3) {
  animation-delay: 1.04s;
}

.load1 view:nth-child(4) {
  animation-delay: 1.17s;
}

.load2 view:nth-child(4) {
  animation-delay: 1.3s;
}

.load3 view:nth-child(4) {
  animation-delay: 1.43s;
}

@-webkit-keyframes load {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}
</style>
