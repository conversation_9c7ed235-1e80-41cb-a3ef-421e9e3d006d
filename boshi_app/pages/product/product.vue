<template>
  <view class="container">
    <swiper class="swiper" indicator-dots circular="true" duration="400">
      <swiper-item class="swiper-item" v-for="(item, index) in 3" :key="index">
        <image :src="detail[`showDetails${index + 1}`]" mode="aspectFill" />
      </swiper-item>
    </swiper>

    <view class="introduce-section">
      <view class="price-box" v-if="detail.plural == 1">
        <text class="price-tip">￥</text>
        <text class="price">{{ detail.money }}</text>
        <text class="m-price">￥{{ detail.marketMoney }}</text>
        <view class="bot-give" v-if="detail.gainIntegral"
          >赠送 {{ detail.gainIntegral }} 积分</view
        >
        <view class="bot-give" v-if="detail.money1"
          >抵扣 {{ detail.money1 }}
        </view>
      </view>
      <view class="price-box" v-if="detail.plural == 2">
        <text class="price">{{ detail.money }}</text>
        <text class="price-tip">积分</text>
        <text class="m-price">{{ detail.marketMoney }}积分</text>
        <view class="bot-give" v-if="detail.gainIntegral"
          >赠送 {{ detail.gainIntegral }} 积分</view
        >
        <view class="bot-give" v-if="detail.money1"
          >抵扣 {{ detail.money1 }}
        </view>
      </view>
      <text class="title">{{ detail.name }}</text>
      <view class="bot-row">
        <text>销量: {{ detail.sales }}</text>
      </view>
      <view class="pluralName">
        <text>{{ detail.pluralName }}</text>
      </view>
    </view>

    <view class="c-list">
      <view class="c-row b-b" @click="toggleSpec('select')">
        <view class="tit">规格</view>
        <view class="con">
          <text class="selected-text"> 已选择：{{ specSelected.name }} </text>
          <text class="yticon icon-you"></text>
        </view>
      </view>
    </view>

    <view class="detail-desc">
      <view class="d-header"><text>图文详情</text></view>
      <rich-text :nodes="detail.contextDetails"></rich-text>
    </view>

    <!-- 底部操作菜单 -->
    <view class="page-bottom">
      <view class="page-bottom-left">
        <navigator
          url="/pages/index/index"
          open-type="switchTab"
          class="p-b-btn"
        >
          <uni-icons type="home" size="20" /> <text>首页</text>
        </navigator>
        <navigator url="/pages/cart/cart" open-type="navigate" class="p-b-btn">
          <uni-icons type="cart" size="20" /> <text>购物车</text>
        </navigator>
        <button open-type="share" class="p-b-btn">
          <uni-icons type="undo" size="20" /> <text>分享</text>
        </button>
      </view>

      <view class="action-btn-group">
        <button
          type="primary"
          class="action-btn no-border buy-now-btn"
          @click="toggleSpec('addCart')"
        >
          加入购物车
        </button>
        <button
          type="primary"
          class="action-btn no-border add-cart-btn"
          @click="toggleSpec('buy')"
        >
          立即购买
        </button>
      </view>
    </view>
    <!-- 规格-模态层弹窗 -->
    <uni-popup ref="specPopup" type="bottom">
      <view class="layer attr-content" @click.stop="stopPrevent">
        <view class="a-t">
          <image :src="detail.showPic | jointPic"></image>
          <view class="right">
            <text class="price" v-if="detail.plural == 1">
              <text>￥</text> {{ detail.money }}
            </text>
            <text class="price" v-if="detail.plural == 2">
              {{ detail.money }} <text>积分</text>
            </text>
            <text class="stock">库存：{{ detail.stock }}件</text>
          </view>
        </view>
        <view class="attr-list" v-if="detail.standard != ''">
          <text>规格</text>
          <view class="item-list">
            <text
              v-for="(item, index) in specChildList"
              :key="index"
              class="tit"
              :class="{ selected: item.selected }"
              @click="selectSpec(index, item.pid)"
              >{{ item.name }}</text
            >
          </view>
        </view>
        <view class="attr-list" v-if="detail.sizes != ''">
          <text>尺码</text>
          <view class="item-list">
            <text
              v-for="(item, index) in sizeChildList"
              :key="index"
              class="tit"
              :class="{ selected: item.selected }"
              @click="selectSize(index, item.pid)"
              >{{ item.name }}</text
            >
          </view>
        </view>
        <view class="attr-list" v-if="detail.model != ''">
          <text>型号</text>
          <view class="item-list">
            <text
              v-for="(item, index) in modelChildList"
              :key="index"
              class="tit"
              :class="{ selected: item.selected }"
              @click="selectModel(index, item.pid)"
              >{{ item.name }}</text
            >
          </view>
        </view>
        <view class="number">
          <text>数量</text> <uni-number-box v-model="vModelValue" />
        </view>
        <button class="btn" @click="subit(btnType)">
          {{ btnType == "addCart" ? "加入购物车" : "立即购买" }}
        </button>
      </view>
    </uni-popup>
    <!-- 分享 -->
    <share ref="share" :contentHeight="580" :shareList="shareList"></share>
  </view>
</template>

<script>
import share from "@/components/share.vue";
import { mapState, mapMutations } from "vuex";
export default {
  components: { share },
  data() {
    return {
      goodsId: "",
      detail: {},
      specClass: "none",
      favorite: true,
      shareList: [],

      specSelected: {},
      specList: [{ id: 1, pid: 1, name: "规格" }],
      specChildList: [],

      sizeSelected: {},
      sizeList: [{ id: 1, pid: 1, name: "尺码" }],
      sizeChildList: [],

      modelSelected: {},
      modelList: [{ id: 1, pid: 1, name: "型号" }],
      modelChildList: [],

      vModelValue: "1",
      btnType: "",
      stutas: "",
    };
  },
  computed: {
    ...mapState(["isLogin", "userInfo"]),
  },
  onShow() {
    this.specChildList = [];
    this.sizeChildList = [];
    this.modelChildList = [];
    this.getData();
  },
  onLoad(options) {
    // #ifdef MP-WEIXIN
    if (!this.isLogin) {
      uni.navigateTo({
        url: `/pages_account/wx_login?inviteCode=${options.inviteCode}`,
      });
    }
    // #endif
    this.goodsId = options.goodsId;
    this.stutas = options.stutas;
  },

  onShareAppMessage(res) {
    if (res.from === "button") {
      // 来自页面内分享按钮
      return {
        title: `商品分享 ${this.detail.name}`, // 标题
        path: `/pages/product/product?goodsId=${this.goodsId}&inviteCode=${this.userInfo.inviteCode}`, // 分享路径
        imageUrl: this.$options.filters["jointPic"](this.detail.showPic), // 分享图
      };
    }
  },
  methods: {
    ...mapMutations(["getDetail", "delDetail"]),
    getData() {
      this.http({
        url: "/api/ssc/scDetails",
        data: {
          id: this.goodsId,
          userId: uni.getStorageSync("userInfo").userId,
        },
        success: (res) => {
          this.detail = res.data;
          this.specInit(this.detail);
          this.detail.contextDetails = this.detail.contextDetails.replace(
            /\<img/gi,
            '<img style="width:100%;height:auto"',
          );
        },
      });
    },
    specInit(data) {
      let standard = data.standard.split("/");
      for (let i = 0; i < standard.length; i++) {
        this.specChildList.push({ id: i, name: standard[i] });
      }
      this.specList.forEach((item) => {
        for (let cItem of this.specChildList) {
          this.$set(cItem, "selected", true);
          this.specSelected = cItem;
          break;
        }
      });

      let sizes = data.sizes.split("/");
      for (let i = 0; i < sizes.length; i++) {
        this.sizeChildList.push({ id: i, name: sizes[i] });
      }
      this.sizeList.forEach((item) => {
        for (let cItem of this.sizeChildList) {
          this.$set(cItem, "selected", true);
          this.sizeSelected = cItem;
          break;
        }
      });

      let model = data.model.split("/");
      for (let i = 0; i < model.length; i++) {
        this.modelChildList.push({ id: i, name: model[i] });
      }
      this.modelList.forEach((item) => {
        for (let cItem of this.modelChildList) {
          this.$set(cItem, "selected", true);
          this.modelSelected = cItem;
          break;
        }
      });
    },

    //规格弹窗开
    toggleSpec(item) {
      this.btnType = item;
      this.$refs.specPopup.open("bottom");
    },
    // 完成
    subit(item) {
      this.$refs.specPopup.close();
      this.delDetail();
      this.specClass = "none";
      switch (item) {
        case "addCart":
          this.addCart();
          break;
        case "buy":
          this.buy();
          break;
      }
    },
    addCart() {
      this.http({
        data: {
          a_d: this.$rsa.encrypt(
            {
              id: this.goodsId,
              standard: this.specSelected.name,
              sizes: this.sizeSelected.name,
              model: this.modelSelected.name,
              count: this.vModelValue,
              userId: uni.getStorageSync("userInfo").userId,
            },
            true,
          ),
          a_m: "cart_cartAdd",
        },
        success: (res) => {
          this.$api.msg(res.message);
        },
      });
    },
    buy() {
      if (this.detail.stock == 0) {
        this.$api.msg("库存不足");
      }
      this.detail.standard = this.specSelected.name;
      this.detail.sizes = this.sizeSelected.name;
      this.detail.model = this.modelSelected.name;
      this.detail.count = this.vModelValue;
      this.getDetail(this.detail);
      uni.navigateTo({
        url: `/pages/order/createOrder?type=buy`,
      });
    },

    //选择规格
    selectSpec(index) {
      let list = this.specChildList;
      list.forEach((item) => {
        this.$set(item, "selected", false);
      });
      this.$set(list[index], "selected", true);
      this.specSelected = [];
      list.forEach((item) => {
        if (item.selected === true) {
          this.specSelected = item;
        }
      });
    },
    selectSize(index) {
      let list = this.sizeChildList;
      list.forEach((item) => {
        this.$set(item, "selected", false);
      });
      this.$set(list[index], "selected", true);
      this.sizeSelected = [];
      list.forEach((item) => {
        if (item.selected === true) {
          this.sizeSelected = item;
        }
      });
    },
    selectModel(index) {
      let list = this.modelChildList;
      list.forEach((item) => {
        this.$set(item, "selected", false);
      });
      this.$set(list[index], "selected", true);
      this.modelSelected = [];
      list.forEach((item) => {
        if (item.selected === true) {
          this.modelSelected = item;
        }
      });
    },
  },
};
</script>

<style lang="scss">
page {
  background: $page-color-base;
  padding-bottom: 100upx;
}

.icon-you {
  font-size: calc(#{$font-base} + 2upx);
  color: #888;
}

.swiper {
  height: 722upx;
  position: relative;

  .swiper-item {
    display: flex;
    justify-content: center;
    align-content: center;
    height: 750upx;
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
    }
  }
}

/* 标题简介 */
.introduce-section {
  background: #fff;
  padding: 20upx 30upx;

  .pluralName {
    display: flex;
    justify-content: flex-end;

    text {
      font-size: 28rpx;
      display: table;
      padding: 5rpx 10rpx;
      border-radius: 6rpx;
      color: $base-color;
      border: 1px solid $base-color;
    }
  }

  .title {
    font-size: 28upx;
    color: $font-color-dark;
    height: 50upx;
    line-height: 50upx;
    font-weight: 600;
  }

  .price-box {
    display: flex;
    align-items: baseline;
    height: 64upx;
    padding: 10upx 0;
    font-size: 26upx;
    color: $uni-color-primary;

    .price-tip {
      font-weight: 600;
    }
  }

  .price {
    font-size: calc(#{$font-lg} + 2upx);
    font-weight: 600;
  }

  .m-price {
    margin: 0 12upx;
    color: $font-color-light;
    text-decoration: line-through;
    font-size: 20upx;
  }

  .coupon-tip {
    align-items: center;
    padding: 4upx 10upx;
    background: $uni-color-primary;
    font-size: $font-sm;
    color: #fff;
    border-radius: 6upx;
    line-height: 1;
    transform: translateY(-4upx);
  }

  .bot-give {
    font-size: 24rpx;
    background-color: $base-color;
    display: table;
    color: #fff;
    padding: 5rpx 10rpx;
    border-radius: 6rpx;
  }

  .bot-row {
    display: flex;
    align-items: center;
    height: 50upx;
    font-size: $font-sm;
    color: $font-color-light;

    text {
      flex: 1;
    }
  }
}

.c-list {
  font-size: calc(#{$font-sm} + 2upx);
  color: $font-color-base;
  background: #fff;
  margin: 10upx 0;

  .c-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20upx 30upx;
    position: relative;
    font-weight: 600;
  }

  .tit {
    width: 140upx;
    color: #000000;
  }

  .con {
    color: $font-color-light;

    .selected-text {
      margin-right: 10upx;
    }
  }

  .bz-list {
    height: 40upx;
    font-size: calc(#{$font-sm} + 2upx);
    color: $font-color-dark;

    text {
      display: inline-block;
      margin-right: 30upx;
    }
  }

  .con-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    color: $font-color-dark;
    line-height: 40upx;
  }

  .red {
    color: $uni-color-primary;
  }
}

/*  详情 */
.detail-desc {
  background: #fff;
  margin-top: 16upx;

  .d-header {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80upx;
    font-size: calc(#{$font-base} + 2upx);
    color: $font-color-dark;
    position: relative;

    text {
      padding: 0 20upx;
      background: #fff;
      position: relative;
      z-index: 1;
    }

    image {
      width: 100vw;
    }

    &:after {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translateX(-50%);
      width: 300upx;
      height: 0;
      content: "";
      border-bottom: 1px solid #ccc;
    }

    iframe {
      width: 100vw;
    }
  }
}

/* 规格选择弹窗 */
.attr-content {
  padding: 30upx;
  background-color: #ffffff;
  border-radius: 10upx 10upx 0 0;
  background-color: #fff;

  .a-t {
    display: flex;
    align-items: center;

    image {
      width: 140upx;
      height: 140upx;
      flex-shrink: 0;
      border-radius: 8upx;
    }

    .right {
      display: flex;
      flex-direction: column;
      padding-left: 34upx;
      font-size: calc(#{$font-sm} + 2upx);
      color: $font-color-base;
      line-height: 42upx;

      .price {
        font-size: $font-lg;
        color: $uni-color-primary;
        margin-bottom: 10upx;
        font-weight: 600;

        text {
          font-weight: 500;
          font-size: 24upx;
        }
      }

      .selected-text {
        margin-right: 10upx;
      }
    }
  }

  .attr-list {
    display: flex;
    flex-direction: column;
    font-size: calc(#{$font-base} + 2upx);
    color: $font-color-base;
    padding-top: 30upx;
    padding-left: 10upx;
  }

  .number {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: calc(#{$font-base} + 2upx);
    color: $font-color-base;
    padding-top: 30upx;
    padding-left: 10upx;

    text {
      font-weight: 600;
      color: #000000;
    }
  }

  .item-list {
    padding: 20upx 0 0;
    display: flex;
    flex-wrap: wrap;

    text {
      display: flex;
      align-items: center;
      justify-content: center;
      background: #eee;
      margin-right: 20upx;
      margin-bottom: 20upx;
      border-radius: 20upx;
      min-width: 60upx;
      height: 40upx;
      padding: 0 20upx;
      font-size: $font-base;
      color: $font-color-dark;
    }

    .selected {
      background: #e4e6fb;
      color: $uni-color-primary;
    }
  }

  .btn {
    height: 66upx;
    line-height: 66upx;
    border-radius: 100upx;
    background: $uni-color-primary;
    font-size: calc(#{$font-base} + 2upx);
    color: #fff;
    width: 88%;
    margin-top: 40rpx;
  }
}

/* 底部操作菜单 */
.page-bottom {
  position: fixed;
  bottom: 0px;
  z-index: 95;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100vw;
  height: 100upx;
  background: #ffffff;
  box-shadow: 0 0 20upx 0 rgba(0, 0, 0, 0.5);
  border-radius: 16upx 16upx 0px 0px;
  box-sizing: border-box;
  padding: 0 10px;

  .page-bottom-left {
    display: flex;
  }

  .p-b-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: $font-sm;
    color: $font-color-base;
    width: 96upx;
    height: 80upx;
    padding: 0;
    margin: 0;
    line-height: normal;
    background-color: transparent;
    border: none;

    &:after {
      border: 0;
    }
  }

  .action-btn-group {
    display: flex;
    height: 76upx;
    border-radius: 100px;
    margin-left: 20upx;
    position: relative;
    border-radius: 50px;

    .buy-now-btn {
      background-color: #fff;
      color: $uni-color-primary;
      border-radius: 50px 0px 0px 50px;
      border: 1px solid $uni-color-primary;
      &::after {
        border: 0;
      }
    }

    .add-cart-btn {
      background-color: $uni-color-primary;
      border-radius: 0px 50px 50px 0px;
    }

    .action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 180upx;
      height: 100%;
      font-size: $font-base;
      padding: 0;
    }
  }
}
</style>
