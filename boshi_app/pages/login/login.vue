<template>
  <view class="login">
    <view class="logo">
      <view class="logo-icon"></view>
      <view class="logo-title">铂时商城</view>
    </view>
    <view class="form">
      <view v-if="isAccountLogin">
        <Input
          v-model="phone"
          placeholder="请输入您的手机号"
          icon="/static/icon/phone_icon.png"
          type="number"
        />
        <Input
          v-model="code"
          placeholder="请输入验证码"
          icon="/static/icon/password_icon.png"
          type="number"
          style="margin-top: 24rpx"
        >
          <template v-slot:right>
            <view
              class="code"
              @click="getCode"
              :style="{
                backgroundColor: countdown > 0 ? '#E1CCFE' : '#7414ff',
              }"
              >{{ codeText }}
            </view>
          </template>
        </Input>
      </view>
      <view v-else>
        <Input
          v-model="username"
          :placeholder="isAccountLogin ? '请输入您的手机号' : '请输入您的账号'"
          icon="/static/icon/account.png"
          type="number"
        />
        <Input
          v-model="password"
          :placeholder="isAccountLogin ? '请输入验证码' : '请输入您的密码'"
          icon="/static/icon/password.png"
          :type="passwordVisible ? 'text' : 'password'"
          style="margin-top: 24rpx"
          @input="onPasswordInput"
        >
          <template v-slot:right>
            <image
              :src="
                passwordVisible
                  ? '/static/icon/visible.png'
                  : '/static/icon/invisible.png'
              "
              @click="togglePasswordVisible"
              style="width: 40rpx; height: 40rpx"
            ></image>
          </template>
        </Input>
      </view>
      <view class="login-btn">
        <view @click="goToRegister">注册</view>
        <view v-if="!isAccountLogin" @click="isAccountLogin = !isAccountLogin"
          >忘记密码？
        </view>
        <view v-else @click="isAccountLogin = !isAccountLogin"> 密码登录</view>
      </view>
      <view
        class="submit-btn"
        :style="{ backgroundColor: canLogin ? '#7414FF' : '#c3c3c3' }"
        @click="submitLogin"
      >
        登录
      </view>
    </view>
    <!--<view class="other-login">-->
    <!--  <view class="other-title">其他登录方式</view>-->
    <!--  <image-->
    <!--    src="/static/loginSignup/weChat.png"-->
    <!--    style="width: 110rpx; height: 110rpx"-->
    <!--  ></image>-->
    <!--</view>-->
    <view class="agreement"
      >注册即代表你同意
      <text class="color-7414FF">《用户协议》</text>
      与
      <text class="color-7414FF"> 《隐私政策》</text>
      以及
      <text class="color-7414FF">《中国移动认证服务条款》</text>
    </view>
  </view>
</template>

<script>
import Input from "@/components/common/Input.vue";
import { switchTab } from "@/utils/navGetTo";
// 不再需要导入工具函数，直接使用 uni API

export default {
  components: { Input },
  data() {
    return {
      isRegister: false,
      isAccountLogin: false,
      username: "",
      password: "",
      phone: "",
      code: "",
      countdown: 0,
      timer: null,
      passwordVisible: false,
    };
  },
  computed: {
    codeText() {
      return this.countdown > 0 ? `${this.countdown}S` : "获取验证码";
    },
    canLogin() {
      if (this.isAccountLogin) {
        // 手机号验证码登录，手机号非空且验证码长度 >= 4
        return this.phone !== "" && this.code.length >= 4;
      } else {
        // 账号密码登录，账号非空且密码长度 >= 6
        return this.username !== "" && this.password.length >= 6;
      }
    },
  },
  methods: {
    getCode() {
      if (this.countdown > 0) {
        return;
      }
      this.countdown = 60;
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(this.timer);
          this.timer = null;
        }
      }, 1000);
    },
    togglePasswordVisible() {
      this.passwordVisible = !this.passwordVisible;
    },
    // 密码输入验证 - 只允许大小写字母、数字、下划线
    validatePasswordInput(value) {
      // 正则表达式：只允许大小写字母、数字、下划线
      const regex = /^[a-zA-Z0-9_]*$/;
      return regex.test(value);
    },
    // 密码输入事件处理
    onPasswordInput(value) {
      // 如果是验证码模式，不限制输入（验证码可能包含其他字符）
      if (this.isAccountLogin) {
        this.password = value;
        return;
      }

      // 密码模式下限制输入
      if (this.validatePasswordInput(value)) {
        this.password = value;
      } else {
        // 过滤掉不符合规则的字符
        this.password = value.replace(/[^a-zA-Z0-9_]/g, "");
        uni.showToast({
          title: "密码只能包含字母、数字和下划线",
          icon: "none",
          duration: 1500,
        });
      }
    },
    // 跳转到注册页面
    goToRegister() {
      console.log("点击注册按钮");

      // 获取当前页面栈
      const pages = getCurrentPages();
      console.log("当前页面栈长度:", pages.length);

      // 检查页面栈中是否有注册页面
      let hasRegisterPage = false;
      if (pages.length > 1) {
        for (let i = pages.length - 2; i >= 0; i--) {
          console.log(`页面栈[${i}]:`, pages[i].route);
          if (pages[i].route === "pages/register/register") {
            hasRegisterPage = true;
            console.log("发现注册页面在页面栈中，使用 navigateBack");
            uni.navigateBack({
              delta: pages.length - 1 - i,
            });
            return;
          }
        }
      }

      if (!hasRegisterPage) {
        console.log("页面栈中没有注册页面，使用 navigateTo 新开");
        uni.navigateTo({
          url: "/pages/register/register",
        });
      }
    },
    submitLogin() {
      if (!this.canLogin) {
        uni.showToast({
          title: "请填写完整信息",
          icon: "none",
        });
        return;
      }
      if (this.isAccountLogin) {
        // 手机号验证码登录逻辑
        console.log("手机号登录", this.phone, this.code);
      } else {
        // 账号密码登录逻辑
        console.log("账号密码登录", this.username, this.password);
      }
      switchTab("/pages/index/index");
    },
  },
};
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100vh;
  position: relative;
  box-sizing: border-box;
  background-image: url("@/static/loginSignup/bg.png");
  background-color: #f8f8f8;
  background-size: 100%;
  background-repeat: no-repeat;
  padding-top: 232rpx;

  .logo {
    margin: 0 auto 80rpx;
    display: flex;
    gap: 11rpx;
    align-items: center;
    justify-content: center;
    width: 375rpx;

    .logo-icon {
      width: 64rpx;
      height: 64rpx;
      border-radius: 16rpx;
      background-color: #7413ff;
    }

    .logo-title {
      font-size: 64rpx;
      color: #1f1f1f;
      line-height: 90rpx;
      height: 90rpx;
      font-family: 阿里巴巴普惠体 2 105 Heavy;
      font-weight: bold;
    }
  }

  .form {
    padding: 0 64rpx;

    .code {
      text-align: center;
      width: 142rpx;
      height: 48rpx;
      border-radius: 24rpx;
      padding: 0 16rpx;
      font-size: 22rpx;
      font-family: 苹方-简 中黑体;
      line-height: 48rpx;
      box-sizing: border-box;
      color: #ffffff;
    }

    .login-btn {
      margin: 32rpx 0 40rpx;
      font-size: 26rpx;
      color: #7414ff;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .submit-btn {
      width: 622rpx;
      height: 88rpx;
      box-sizing: border-box;
      background-color: #c3c3c3;
      font-size: 32rpx;
      color: #ffffff;
      line-height: 88rpx;
      text-align: center;
      font-weight: bold;
      border-radius: 24rpx;
      font-family: HarmonyOS Sans SC Black;
    }
  }

  .other-login {
    position: absolute;
    bottom: 210rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 610rpx;
    display: flex;
    flex-direction: column;
    color: #b2b2b2;
    font-size: 26rpx;
    gap: 32rpx;
    align-items: center;
    justify-content: center;

    .other-title {
      position: relative;

      &::before {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: -148rpx;
        content: "";
        display: block;
        width: 106rpx;
        height: 0;
        border: 1rpx solid;
        border-image: linear-gradient(
            270deg,
            rgba(200, 200, 200, 1),
            rgba(151, 151, 151, 0)
          )
          1 1;
      }

      &::after {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -148rpx;
        content: "";
        display: block;
        width: 106rpx;
        height: 0;
        border: 1rpx solid;
        border-image: linear-gradient(
            -270deg,
            rgba(200, 200, 200, 1),
            rgba(151, 151, 151, 0)
          )
          1 1;
      }
    }
  }

  .agreement {
    color: #b2b2b2;
    font-size: 26rpx;
    text-align: center;
    position: absolute;
    width: 598rpx;
    left: 50%;
    transform: translateX(-50%);
    bottom: 32rpx;
  }
}

.color-7414FF {
  color: #7414ff;
}
</style>
