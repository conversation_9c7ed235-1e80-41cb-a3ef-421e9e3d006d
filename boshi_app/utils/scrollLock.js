/**
 * 页面滚动锁定工具
 * 用于在弹窗显示时阻止背景页面滚动
 */

class ScrollLock {
  constructor() {
    this.locked = false;
    this.scrollTop = 0;
    this.bodyStyle = {};
  }

  /**
   * 锁定滚动
   */
  lock() {
    if (this.locked) return;
    
    this.locked = true;
    
    // 记录当前滚动位置
    this.scrollTop = uni.getSystemInfoSync().scrollTop || 0;
    
    // H5端处理
    // #ifdef H5
    this.lockH5();
    // #endif
    
    // 小程序端处理
    // #ifdef MP
    this.lockMiniProgram();
    // #endif
    
    // App端处理
    // #ifdef APP-PLUS
    this.lockApp();
    // #endif
  }

  /**
   * 解锁滚动
   */
  unlock() {
    if (!this.locked) return;
    
    this.locked = false;
    
    // H5端处理
    // #ifdef H5
    this.unlockH5();
    // #endif
    
    // 小程序端处理
    // #ifdef MP
    this.unlockMiniProgram();
    // #endif
    
    // App端处理
    // #ifdef APP-PLUS
    this.unlockApp();
    // #endif
  }

  /**
   * H5端锁定滚动
   */
  lockH5() {
    const body = document.body;
    const html = document.documentElement;
    
    // 保存原始样式
    this.bodyStyle = {
      overflow: body.style.overflow,
      position: body.style.position,
      top: body.style.top,
      left: body.style.left,
      width: body.style.width,
      height: body.style.height
    };
    
    // 设置固定定位阻止滚动
    body.style.overflow = 'hidden';
    body.style.position = 'fixed';
    body.style.top = `-${this.scrollTop}px`;
    body.style.left = '0';
    body.style.width = '100%';
    body.style.height = '100%';
  }

  /**
   * H5端解锁滚动
   */
  unlockH5() {
    const body = document.body;
    
    // 恢复原始样式
    Object.keys(this.bodyStyle).forEach(key => {
      body.style[key] = this.bodyStyle[key] || '';
    });
    
    // 恢复滚动位置
    if (this.scrollTop > 0) {
      window.scrollTo(0, this.scrollTop);
    }
  }

  /**
   * 小程序端锁定滚动
   */
  lockMiniProgram() {
    // 小程序端通过设置页面样式来阻止滚动
    try {
      uni.pageScrollTo({
        scrollTop: this.scrollTop,
        duration: 0
      });
    } catch (e) {
      console.warn('小程序滚动锁定失败:', e);
    }
  }

  /**
   * 小程序端解锁滚动
   */
  unlockMiniProgram() {
    // 小程序端恢复滚动
    try {
      uni.pageScrollTo({
        scrollTop: this.scrollTop,
        duration: 0
      });
    } catch (e) {
      console.warn('小程序滚动解锁失败:', e);
    }
  }

  /**
   * App端锁定滚动
   */
  lockApp() {
    // App端处理方式
    console.log('App端滚动锁定');
  }

  /**
   * App端解锁滚动
   */
  unlockApp() {
    // App端处理方式
    console.log('App端滚动解锁');
  }

  /**
   * 获取当前锁定状态
   */
  isLocked() {
    return this.locked;
  }
}

// 创建单例实例
const scrollLock = new ScrollLock();

export default scrollLock;
