// 构建完整的 URL（包含查询参数）
function buildUrl(url, params = {}) {
  // 如果 URL 不包含查询参数且有参数对象，构建查询字符串
  if (!url.includes('?') && Object.keys(params).length > 0) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    return `${url}?${queryString}`;
  }
  return url;
}

// 新开页面跳转
export function navTo(url, params = {}) {
  return new Promise((resolve, reject) => {
    const finalUrl = buildUrl(url, params);

    uni.navigateTo({
      url: finalUrl,
      success: (res) => {
        console.log('页面跳转成功:', finalUrl);
        resolve(res);
      },
      fail: (err) => {
        console.error('页面跳转失败:', err);
        reject(err);
      }
    });
  });
}
// 返回
export function navBack(delta = 1) {
  return new Promise((resolve, reject) => {
    uni.navigateBack({
      delta,
      success: (res) => {
        console.log('页面返回成功');
        resolve(res);
      },
      fail: (err) => {
        console.error('页面返回失败:', err);
        reject(err);
      }
    });
  });
}
// 跳转 switchTab
export function switchTab(url, params = {}) {
  return new Promise((resolve, reject) => {
    console.log("switchTab", url, params);

    // switchTab 不支持查询参数，只能跳转到 tabBar 页面
    const baseUrl = url.includes('?') ? url.split('?')[0] : url;

    uni.switchTab({
      url: baseUrl,
      success: (res) => {
        console.log('switchTab 跳转成功:', baseUrl);
        resolve(res);
      },
      fail: (err) => {
        console.error('switchTab 跳转失败:', err);
        reject(err);
      }
    });
  });
}
// 关闭当前页面，跳转到应用内的某个页面
export function redirectTo(url, params = {}) {
  return new Promise((resolve, reject) => {
    const finalUrl = buildUrl(url, params);

    uni.redirectTo({
      url: finalUrl,
      success: (res) => {
        console.log('redirectTo 跳转成功:', finalUrl);
        resolve(res);
      },
      fail: (err) => {
        console.error('redirectTo 跳转失败:', err);
        reject(err);
      }
    });
  });
}
