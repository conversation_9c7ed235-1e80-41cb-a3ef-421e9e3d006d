<template>
  <view class="media-uploader">
    <!-- 媒体列表展示 -->
    <view class="media-grid">
      <view
        v-for="(item, index) in mediaList"
        :key="index"
        class="media-item"
        @longpress="deleteMedia(index)"
      >
        <!-- 图片预览 -->
        <view v-if="item.type === 'image'" class="media-preview">
          <!-- 上传成功后显示图片 -->
          <image
            v-if="item.uploaded && item.url"
            :src="item.url"
            class="image-preview"
            mode="aspectFill"
            @click="previewImage(item.url)"
          />
          <!-- 上传中显示加载动画 -->
          <view v-if="item.uploading" class="image-uploading-overlay">
            <view class="uploading-spinner"></view>
            <view class="uploading-text">上传中</view>
          </view>

        </view>
        <!-- 视频预览 -->
        <view
          v-else-if="item.type === 'video'"
          class="media-preview video-preview"
          @click="previewVideo(item)"
        >
          <!-- 上传中遮罩层 -->
          <view v-if="item.uploading" class="video-uploading-overlay">
            <view class="uploading-spinner"></view>
            <view class="uploading-text">上传中</view>
          </view>
          <!-- 优先显示服务器返回的封面图片 -->
          <image
            v-else-if="item.poster && item.uploaded"
            :src="item.poster"
            class="video-poster"
            mode="aspectFill"
            @error="handleImageError(item, index)"
          />
          <!-- 显示本地封面（上传前） -->
          <image
            v-else-if="item.poster && !item.posterError"
            :src="item.poster"
            class="video-poster"
            mode="aspectFill"
            @error="handleImageError(item, index)"
          />
          <!-- 最终备用方案：渐变背景 -->
          <view v-else class="video-placeholder">
            <view class="video-icon">📹</view>
          </view>
          <!-- 播放按钮遮罩层 -->
          <view v-if="!item.uploading" class="video-play-overlay">
            <view class="video-play-btn">
              <view class="play-triangle"></view>
            </view>
          </view>
        </view>
        <!-- 删除按钮 -->
        <view class="delete-btn" @click="deleteMedia(index)">
          <uni-icons type="clear" size="18" color="#fff"></uni-icons>
        </view>
      </view>

      <!-- 添加按钮 -->
      <view
        v-if="mediaList.length < limit"
        class="add-btn"
        @click="showActionSheet"
      >
        <uni-icons type="plusempty" size="60" color="#ccc"></uni-icons>
      </view>
    </view>

    <!-- 操作选择弹窗 -->
    <uni-popup ref="actionPopup" type="bottom">
      <view class="action-sheet">
        <view class="action-header">选择操作</view>
        <view class="action-list">
          <view class="action-item" @click="chooseFromAlbum('image')">
            <uni-icons type="image" size="24" color="#333"></uni-icons>
            <text>从相册选择图片</text>
          </view>
          <view class="action-item" @click="chooseFromAlbum('video')">
            <uni-icons type="videocam" size="24" color="#333"></uni-icons>
            <text>从相册选择视频</text>
          </view>
          <view class="action-item" @click="takePhoto">
            <uni-icons type="camera" size="24" color="#333"></uni-icons>
            <text>拍摄照片</text>
          </view>
          <view class="action-item" @click="recordVideo">
            <uni-icons
              type="videocam-filled"
              size="24"
              color="#333"
            ></uni-icons>
            <text>录制视频</text>
          </view>
        </view>
        <view class="action-cancel" @click="hideActionSheet">取消</view>
      </view>
    </uni-popup>

    <!-- 临时视频播放器（App端全屏播放用） -->
    <video
      v-if="showTempVideo"
      id="tempVideoPlayer"
      :src="tempVideoUrl"
      :style="tempVideoStyle"
      :controls="true"
      :autoplay="false"
      :preload="metadata"
      :show-center-play-btn="false"
      :show-fullscreen-btn="true"
      :show-play-btn="true"
      :show-progress="true"
      :enable-progress-gesture="true"
      object-fit="contain"
      @fullscreenchange="onTempVideoFullscreenChange"
      @play="onTempVideoPlay"
      @pause="onTempVideoPause"
      @error="onTempVideoError"
    />

    <!-- 自定义删除确认弹窗 -->
    <uni-popup ref="deleteConfirmPopup" type="center">
      <view class="delete-confirm-modal">
        <view class="delete-confirm-title">确认删除</view>
        <view class="delete-confirm-content">确定要删除这个文件吗？</view>
        <view class="delete-confirm-actions">
          <view class="delete-confirm-btn cancel" @click="cancelDelete"
            >取消</view
          >
          <view class="delete-confirm-btn confirm" @click="confirmDelete"
            >删除</view
          >
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: "MediaUploader",
  props: {
    // 媒体列表
    value: {
      type: Array,
      default: () => [],
    },
    // 最大上传数量
    limit: {
      type: Number,
      default: 9,
    },
    // 标题
    title: {
      type: String,
      default: "添加媒体",
    },
    // 支持的媒体类型 'image' | 'video' | 'both'
    mediaType: {
      type: String,
      default: "both",
    },
    // 图片压缩质量 0-100
    imageQuality: {
      type: Number,
      default: 80,
    },
    // 视频最大时长（秒）
    maxDuration: {
      type: Number,
      default: 60,
    },
    // 是否自动上传
    autoUpload: {
      type: Boolean,
      default: false,
    },
    // 上传接口地址（已废弃，使用下面的具体接口）
    uploadUrl: {
      type: String,
      default: "",
    },
    // 视频上传接口
    videoUploadUrl: {
      type: String,
      default: "/fe/aliVideoUpload",
    },
    // 图片上传接口
    imageUploadUrl: {
      type: String,
      default: "/fe/aliImageUpload",
    },
    // 服务器基础URL
    baseUrl: {
      type: String,
      default: "https://boshi.channce.com/boshi",
    },
  },
  data() {
    return {
      mediaList: [],
      deleteIndex: -1,
      isApp: false,
      // 临时视频播放相关
      showTempVideo: false,
      tempVideoUrl: '',
      tempVideoStyle: 'position: fixed; top: -9999px; left: -9999px; width: 1px; height: 1px; opacity: 0;'
    };
  },

  mounted() {
    // 检测是否为App端
    // #ifdef APP-PLUS
    this.isApp = true;
    console.log("当前运行在App端");
    // #endif
  },
  watch: {
    value: {
      handler(newVal) {
        this.mediaList = newVal || [];
      },
      immediate: true,
      deep: true,
    },
    mediaList: {
      handler(newVal) {
        this.$emit("input", newVal);
        this.$emit("change", newVal);
      },
      deep: true,
    },
  },
  methods: {
    // 显示操作选择弹窗
    showActionSheet() {
      this.$refs.actionPopup.open();
    },

    // 隐藏操作选择弹窗
    hideActionSheet() {
      this.$refs.actionPopup.close();
    },

    // 从相册选择
    chooseFromAlbum(type) {
      this.hideActionSheet();

      if (type === "image") {
        this.chooseImage("album");
      } else if (type === "video") {
        this.chooseVideo("album");
      }
    },

    // 拍摄照片
    takePhoto() {
      this.hideActionSheet();
      this.chooseImage("camera");
    },

    // 录制视频
    recordVideo() {
      this.hideActionSheet();
      this.chooseVideo("camera");
    },

    // 选择图片
    chooseImage(sourceType) {
      const count = this.limit - this.mediaList.length;
      if (count <= 0) {
        uni.showToast({
          title: `最多只能上传${this.limit}个文件`,
          icon: "none",
        });
        return;
      }

      uni.chooseImage({
        count: count,
        sourceType: [sourceType],
        success: (res) => {
          // 逐个处理图片
          res.tempFilePaths.forEach((path) => {
            const mediaItem = {
              type: "image",
              url: '',  // 初始为空，只有上传成功后才设置
              tempPath: path,
              uploaded: false,
              uploading: true,
            };
            this.mediaList.push(mediaItem);

            // 单个上传图片到服务器
            this.uploadSingleImageToServer(mediaItem);
          });
        },
        fail: (err) => {
          console.error("选择图片失败:", err);
          uni.showToast({
            title: "选择图片失败",
            icon: "none",
          });
        },
      });
    },

    // 选择视频
    chooseVideo(sourceType) {
      if (this.mediaList.length >= this.limit) {
        uni.showToast({
          title: `最多只能上传${this.limit}个文件`,
          icon: "none",
        });
        return;
      }

      uni.chooseVideo({
        sourceType: [sourceType],
        maxDuration: this.maxDuration,
        camera: "back",
        success: (res) => {
          console.log("选择视频成功:", res);
          const mediaItem = {
            type: "video",
            url: res.tempFilePath,
            tempPath: res.tempFilePath,
            poster: res.thumbTempFilePath,
            duration: res.duration,
            size: res.size,
            uploaded: false,
            uploading: false,
          };
          console.log("视频媒体项:", mediaItem);

          this.mediaList.push(mediaItem);

          // 立即上传视频到服务器
          this.uploadVideoToServer(mediaItem);
        },
        fail: (err) => {
          console.error("选择视频失败:", err);
          uni.showToast({
            title: "选择视频失败",
            icon: "none",
          });
        },
      });
    },

    // 预览图片
    previewImage(url) {
      const imageUrls = this.mediaList
        .filter((item) => item.type === "image" && item.uploaded)
        .map((item) => item.url);

      // #ifdef APP-PLUS
      // App端使用自定义预览，确保适配状态栏
      uni.previewImage({
        current: url,
        urls: imageUrls,
        success: () => {
          // 隐藏状态栏以获得更好的预览体验
          // uni.hideNavigationBarLoading();
        },
        fail: (err) => {
          console.error('图片预览失败:', err);
          uni.showToast({
            title: '图片预览失败',
            icon: 'none'
          });
        }
      });
      // #endif

      // #ifndef APP-PLUS
      uni.previewImage({
        current: url,
        urls: imageUrls,
      });
      // #endif
    },

    // 预览视频 - 使用系统预览机制
    previewVideo(videoItem) {
      console.log("=== 预览视频 ===");
      console.log("视频项数据:", videoItem);

      // 如果视频还在上传中，不允许预览
      if (videoItem.uploading) {
        uni.showToast({
          title: "视频上传中，请稍候",
          icon: "none",
        });
        return;
      }

      // 获取视频URL
      const videoUrl = videoItem.uploaded ? videoItem.url : videoItem.tempPath;

      if (!videoUrl) {
        uni.showToast({
          title: "视频路径无效",
          icon: "none",
        });
        return;
      }

      console.log("预览视频URL:", videoUrl);

      // 使用 previewImage 调用系统预览，然后劫持页面进行视频替换
      this.previewVideoWithImageHijack(videoUrl);
    },

    // 使用图片预览劫持方案播放视频
    previewVideoWithImageHijack(videoUrl) {
      // #ifdef H5
      // H5端使用劫持方案
      this.setupVideoHijackForH5(videoUrl);

      // 调用系统预览
      uni.previewImage({
        current: 0,
        urls: [videoUrl], // 将视频URL作为图片URL传入
        success: () => {
          console.log('预览页面打开成功，开始视频劫持');
        },
        fail: (err) => {
          console.error('预览页面打开失败:', err);
          // 降级方案：使用临时video方案
          this.createTempVideoPlayer(videoUrl);
        }
      });
      // #endif

      // #ifdef APP-PLUS
      // App端直接使用临时视频播放器，不调用 previewImage
      console.log('App端直接使用临时视频播放器');
      this.setupVideoHijackForApp(videoUrl);
      // #endif
    },



    // H5端视频劫持设置
    setupVideoHijackForH5(videoUrl) {
      // 多次尝试劫持，因为预览页面加载时间不确定
      let attempts = 0;
      const maxAttempts = 10;

      const tryHijack = () => {
        attempts++;
        console.log(`第${attempts}次尝试劫持预览页面`);

        const success = this.hijackPreviewPageForVideo(videoUrl);

        if (!success && attempts < maxAttempts) {
          // 如果劫持失败且未达到最大尝试次数，继续尝试
          setTimeout(tryHijack, 300);
        } else if (!success) {
          console.log('劫持失败，使用降级方案');
          // 关闭预览页面
          this.closePreviewPage();
          // 使用降级方案
          setTimeout(() => {
            this.createTempVideoPlayer(videoUrl);
          }, 500);
        }
      };

      // 首次尝试延迟500ms
      setTimeout(tryHijack, 500);
    },

    // App端视频劫持设置
    setupVideoHijackForApp(videoUrl) {
      // App端不调用 previewImage，直接使用临时视频播放器
      console.log('App端直接使用临时视频播放器');

      // 立即显示加载提示
      uni.showToast({
        title: '正在准备视频播放器...',
        icon: 'loading',
        duration: 2000
      });

      // 立即创建视频播放器
      this.createTempVideoPlayer(videoUrl);
    },

    // 关闭预览页面（H5端）
    closePreviewPage() {
      // #ifdef H5
      try {
        // 尝试关闭当前窗口或返回上一页
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.close();
        }
      } catch (error) {
        console.error('关闭预览页面失败:', error);
      }
      // #endif
    },

    // 劫持预览页面，将图片替换为视频
    hijackPreviewPageForVideo(videoUrl) {
      // #ifdef H5
      try {
        // 检查是否为视频格式
        if (!this.isVideoFormat(videoUrl)) {
          console.log('不是视频格式，跳过劫持');
          return false;
        }

        // 查找预览页面中的图片元素
        const images = document.querySelectorAll('img');
        console.log('找到图片元素数量:', images.length);

        if (images.length === 0) {
          console.log('未找到图片元素，劫持失败');
          return false;
        }

        let replaced = false;

        images.forEach((img, index) => {
          // 检查图片src是否匹配当前视频URL
          if (img.src && (img.src === videoUrl || img.src.includes(videoUrl))) {
            console.log('找到匹配的图片元素，开始替换为视频:', img.src);
            this.replaceImageWithVideo(img, videoUrl);
            replaced = true;
          }
        });

        // 如果没有找到匹配的图片，尝试替换第一个图片
        if (!replaced && images.length > 0) {
          console.log('未找到匹配图片，替换第一个图片元素为视频');
          this.replaceImageWithVideo(images[0], videoUrl);
          replaced = true;
        }

        return replaced;
      } catch (error) {
        console.error('H5端视频劫持失败:', error);
        return false;
      }
      // #endif

      // #ifdef APP-PLUS
      // App端暂时使用降级方案
      console.log('App端暂不支持DOM劫持，使用降级方案');
      return false;
      // #endif
    },

    // 判断是否为视频格式
    isVideoFormat(url) {
      if (!url) return false;

      const videoExtensions = [
        '.mp4', '.mov', '.avi', '.wmv', '.flv', '.webm', '.mkv',
        '.m4v', '.3gp', '.ogv', '.ts', '.m3u8', '.f4v', '.asf'
      ];

      const urlLower = url.toLowerCase();

      // 检查文件扩展名
      const hasVideoExtension = videoExtensions.some(ext => urlLower.includes(ext));

      // 检查MIME类型（如果URL中包含）
      const hasVideoMime = urlLower.includes('video/');

      return hasVideoExtension || hasVideoMime;
    },

    // 将图片元素替换为视频元素
    replaceImageWithVideo(imgElement, videoUrl) {
      try {
        // 创建video元素
        const video = document.createElement('video');
        video.src = videoUrl;
        video.controls = true;
        video.autoplay = false; // 改为手动播放，避免自动播放策略问题
        video.loop = false;
        video.preload = 'metadata';
        video.playsInline = true; // 支持内联播放

        // CORS处理：先尝试不设置crossOrigin，如果失败再尝试其他方案
        // video.crossOrigin = 'anonymous'; // 暂时注释掉，因为可能导致CORS问题

        // 复制图片的样式和属性
        video.style.cssText = imgElement.style.cssText;
        video.style.width = imgElement.style.width || '100%';
        video.style.height = imgElement.style.height || 'auto';
        video.style.maxWidth = '100%';
        video.style.maxHeight = '100%';
        video.style.objectFit = 'contain';
        video.style.backgroundColor = '#000';

        // 复制类名
        video.className = imgElement.className;

        // 添加加载指示器
        const loadingDiv = document.createElement('div');
        loadingDiv.innerHTML = '视频加载中...';
        loadingDiv.style.cssText = `
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          background: rgba(0,0,0,0.7);
          padding: 10px 20px;
          border-radius: 5px;
          font-size: 14px;
          z-index: 1000;
        `;

        // 添加视频事件监听
        video.addEventListener('loadedmetadata', () => {
          console.log('视频元数据加载完成');
          // 移除加载指示器
          if (loadingDiv.parentNode) {
            loadingDiv.parentNode.removeChild(loadingDiv);
          }
        });

        video.addEventListener('canplay', () => {
          console.log('视频可以播放');
          // 移除加载指示器
          if (loadingDiv.parentNode) {
            loadingDiv.parentNode.removeChild(loadingDiv);
          }
        });

        video.addEventListener('error', (e) => {
          console.error('视频加载失败:', e);
          // 移除加载指示器
          if (loadingDiv.parentNode) {
            loadingDiv.parentNode.removeChild(loadingDiv);
          }

          // H5端CORS专项处理
          // #ifdef H5
          // 如果是跨域导致的失败，尝试开启匿名跨域属性并重试一次
          if (!video.crossOrigin) {
            console.log('尝试开启crossOrigin=anonymous后重试');
            video.crossOrigin = 'anonymous';
            // 重新设置src触发加载
            const old = video.src;
            video.src = '';
            setTimeout(() => { video.src = old; }, 0);
            return;
          }
          // #endif

          // 如果视频加载失败，恢复图片
          imgElement.style.display = 'block';
          if (video.parentNode) {
            video.parentNode.removeChild(video);
          }

          // 显示错误提示 + 备用方案
          const errorDiv = document.createElement('div');
          errorDiv.innerHTML = '视频加载失败，点击使用新窗口播放';
          errorDiv.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            background: rgba(255,0,0,0.7);
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            z-index: 1000;
          `;
          errorDiv.onclick = () => {
            // 备用：在新窗口/标签打开视频（绕过CORS限制）
            window.open(videoUrl, '_blank');
            if (errorDiv.parentNode) {
              errorDiv.parentNode.removeChild(errorDiv);
            }
          };

          if (imgElement.parentNode) {
            imgElement.parentNode.appendChild(errorDiv);
          }
        });

        // 替换元素
        if (imgElement.parentNode) {
          // 设置父元素为相对定位，以便加载指示器正确定位
          imgElement.parentNode.style.position = 'relative';

          imgElement.parentNode.insertBefore(video, imgElement);
          imgElement.parentNode.appendChild(loadingDiv);
          imgElement.style.display = 'none'; // 隐藏原图片而不是删除，以便出错时恢复

          console.log('视频元素替换成功');
        }
      } catch (error) {
        console.error('替换图片为视频失败:', error);
      }
    },

    // 创建临时视频播放器（降级方案）
    createTempVideoPlayer(videoUrl) {
      console.log('=== 创建临时视频播放器 ===');
      console.log('视频URL:', videoUrl);

      // 设置临时视频数据
      this.tempVideoUrl = videoUrl;
      this.showTempVideo = true;

      // 保持视频隐藏，直接准备全屏播放
      this.tempVideoStyle = 'position: fixed; top: -9999px; left: -9999px; width: 1px; height: 1px; opacity: 0;';

      console.log('临时视频数据已设置:', {
        tempVideoUrl: this.tempVideoUrl,
        showTempVideo: this.showTempVideo
      });

      // 显示智能加载提示
      const loadingToast = uni.showToast({
        title: '正在打开视频...',
        icon: 'loading',
        duration: 10000, // 设置较长时间，但会在成功时手动关闭
        mask: true
      });

      // 等待DOM更新后请求全屏
      this.$nextTick(() => {
        console.log('DOM更新完成，准备创建视频上下文');

        try {
          const videoContext = uni.createVideoContext('tempVideoPlayer', this);
          console.log('视频上下文创建成功:', videoContext);

          // 极速启动 - 同时播放和全屏
          setTimeout(() => {
            console.log('极速启动播放和全屏');

            try {
              // 同时启动播放和全屏请求
              videoContext.play();
              videoContext.requestFullScreen({
                direction: 0 // 0: 正常竖向, 90: 屏幕逆时针90度, -90: 屏幕顺时针90度
              });

              console.log('播放和全屏请求已同时发送');

            } catch (error) {
              console.error('视频播放或全屏失败:', error);
              // 如果失败，使用备用方案
              this.fallbackVideoPlay(videoUrl);
            }
          }, 200); // 进一步减少到200ms
        } catch (error) {
          console.error('创建视频上下文失败:', error);
          // 如果创建上下文失败，尝试最后的备用方案
          this.fallbackVideoPlay(videoUrl);
        }
      });
    },

    // 最后的备用方案
    fallbackVideoPlay(videoUrl) {
      console.log('使用最后的备用方案');

      // 隐藏临时视频
      this.showTempVideo = false;
      this.tempVideoUrl = '';

      // #ifdef APP-PLUS
      // 尝试使用系统默认应用打开
      uni.showModal({
        title: '视频播放',
        content: '是否使用外部应用播放视频？',
        success: (res) => {
          if (res.confirm) {
            plus.runtime.openURL(videoUrl);
          }
        }
      });
      // #endif

      // #ifndef APP-PLUS
      // 非App端显示错误信息
      uni.showToast({
        title: '视频播放失败',
        icon: 'none',
        duration: 2000
      });
      // #endif
    },

    // 临时视频全屏状态改变
    onTempVideoFullscreenChange(e) {
      console.log('全屏状态改变:', e);

      if (!e.detail.fullScreen) {
        console.log('退出全屏，直接关闭视频');
        // 退出全屏后直接关闭，不显示小窗口，也不显示提示
        this.showTempVideo = false;
        this.tempVideoUrl = '';
        this.tempVideoStyle = 'position: fixed; top: -9999px; left: -9999px; width: 1px; height: 1px; opacity: 0;';
      } else {
        console.log('进入全屏模式');
        // 进入全屏时确保关闭加载提示
        uni.hideToast();
      }
    },

    // 临时视频播放事件
    onTempVideoPlay(e) {
      console.log('临时视频开始播放:', e);
      // 视频开始播放时关闭加载提示
      uni.hideToast();
    },

    // 临时视频暂停事件
    onTempVideoPause(e) {
      console.log('临时视频暂停:', e);
    },

    // 临时视频错误事件
    onTempVideoError(e) {
      console.error('临时视频播放错误:', e);
      uni.showToast({
        title: '视频播放失败',
        icon: 'none',
        duration: 2000
      });

      // 清理临时视频
      this.showTempVideo = false;
      this.tempVideoUrl = '';

      // 尝试备用方案
      if (this.tempVideoUrl) {
        this.fallbackVideoPlay(this.tempVideoUrl);
      }
    },



    // 手动请求全屏
    requestTempVideoFullscreen() {
      console.log('手动请求全屏');
      try {
        const videoContext = uni.createVideoContext('tempVideoPlayer', this);
        videoContext.requestFullScreen({
          direction: 0 // 0: 正常竖向, 90: 屏幕逆时针90度, -90: 屏幕顺时针90度
        });

        uni.showToast({
          title: '正在进入全屏...',
          icon: 'loading',
          duration: 1000
        });
      } catch (error) {
        console.error('手动请求全屏失败:', error);
        uni.showToast({
          title: '全屏失败',
          icon: 'none',
          duration: 1500
        });
      }
    },




    // 处理视频区域点击 - 用于区分单击和双击
    handleVideoOverlayClick() {
      console.log("=== 视频覆盖层被点击 ===");
      this.closeVideoPreview();
    },

    // 处理视频单击
    handleVideoClick(e) {
      console.log("=== 视频单击 ===");
      e.stopPropagation(); // 阻止事件冒泡到覆盖层
      this.clickCount++;

      if (this.clickTimer) {
        clearTimeout(this.clickTimer);
      }

      this.clickTimer = setTimeout(() => {
        if (this.clickCount === 1) {
          // 单击 - 关闭预览
          console.log("单击 - 关闭视频预览");
          this.closeVideoPreview();
        }
        this.clickCount = 0;
      }, 300); // 300ms内双击有效
    },

    // 处理视频双击
    handleVideoDoubleClick(e) {
      console.log("=== 视频双击 ===");
      e.stopPropagation(); // 阻止事件冒泡

      if (this.clickTimer) {
        clearTimeout(this.clickTimer);
        this.clickTimer = null;
      }
      this.clickCount = 0;

      // 双击 - 切换播放状态
      this.toggleVideoPlay();
    },





    // 视频开始加载
    onVideoLoadStart(e) {
      console.log('视频开始加载:', e);
    },

    // 视频数据加载完成
    onVideoLoadedData(e) {
      console.log('视频数据加载完成:', e);
      console.log('当前视频信息:', this.currentVideo);

      // App端特殊处理
      // #ifdef APP-PLUS
      uni.showToast({
        title: '视频加载完成',
        icon: 'success',
        duration: 1000
      });
      // #endif
    },

    // 视频等待中
    onVideoWaiting(e) {
      console.log('视频缓冲中:', e);
    },

    // 视频可以播放
    onVideoCanPlay(e) {
      console.log('视频可以播放:', e);
      this.showVideoError = false; // 隐藏错误提示

      // App端在视频可播放时尝试显示
      // #ifdef APP-PLUS
      this.$forceUpdate();
      uni.showToast({
        title: '视频准备就绪',
        icon: 'success',
        duration: 1000
      });
      // #endif
    },

    // 视频时间更新
    onVideoTimeUpdate(e) {
      // console.log('视频时间更新:', e.detail.currentTime);
    },

    // 视频播放结束
    onVideoEnded(e) {
      console.log('视频播放结束:', e);
      this.isVideoPlaying = false;
    },

    // 测试视频URL是否可访问
    testVideoUrl(url) {
      console.log('测试视频URL:', url);

      // 简单的URL格式检查
      if (!url) {
        console.error('URL为空');
        return false;
      }

      if (url.startsWith('http://') || url.startsWith('https://')) {
        console.log('网络URL格式正确');
        return true;
      }

      if (url.startsWith('file://')) {
        console.log('本地文件URL格式正确');
        return true;
      }

      console.warn('URL格式可能有问题:', url);
      return false;
    },

    // 删除媒体
    deleteMedia(index) {
      this.deleteIndex = index;
      this.$refs.deleteConfirmPopup.open();
    },

    // 取消删除
    cancelDelete() {
      this.$refs.deleteConfirmPopup.close();
      this.deleteIndex = -1;
    },

    // 确认删除
    confirmDelete() {
      if (this.deleteIndex >= 0) {
        this.mediaList.splice(this.deleteIndex, 1);
      }
      this.$refs.deleteConfirmPopup.close();
      this.deleteIndex = -1;
    },

    // 单个上传图片到服务器
    uploadSingleImageToServer(mediaItem) {
      mediaItem.uploading = true;

      uni.showLoading({
        title: "上传图片中...",
        mask: true
      });

      const uploadUrl = this.baseUrl + this.imageUploadUrl;

      uni.uploadFile({
        url: uploadUrl,
        filePath: mediaItem.tempPath,
        name: "file",
        header: {
          // 添加必要的请求头
          "parentId": "81556ac364d84cc2b77817841dab21d2",
          'ac': uni.getStorageSync('token3'),
          'pid': this.$store?.state?.userInfo?.userId || ''
        },
        success: (res) => {
          try {
            console.log("图片上传响应:", res);

            // 检查HTTP状态码
            if (res.statusCode !== 200) {
              throw new Error(`服务器错误: ${res.statusCode}`);
            }

            const data = JSON.parse(res.data);
            console.log("解析后的响应数据:", data);

            if (data.code === 2000 || data.code === 200 || data.success) {
              // 更新媒体项数据，使用服务器返回的图片URL
              mediaItem.url =
                data.data?.imageUrl || data.data?.url || data.imageUrl || mediaItem.tempPath;
              mediaItem.thumbnail =
                data.data?.thumbnailsUrl || data.thumbnailsUrl;
              mediaItem.uploaded = true;
              mediaItem.uploading = false;

              console.log("图片上传成功，更新数据:", {
                originalUrl: mediaItem.tempPath,
                newUrl: mediaItem.url,
                thumbnail: mediaItem.thumbnail,
                fullData: data,
              });

              this.$emit("upload-success", mediaItem);

              uni.showToast({
                title: "图片上传成功",
                icon: "success",
              });
            } else {
              throw new Error(data.message || "上传失败");
            }
          } catch (error) {
            console.error("图片上传失败:", error);
            // 上传失败时直接从列表中移除该项目
            const index = this.mediaList.findIndex(item => item === mediaItem);
            if (index > -1) {
              this.mediaList.splice(index, 1);
            }
            this.$emit("upload-error", error);
            uni.showToast({
              title: "图片上传失败",
              icon: "none",
            });
          }
        },
        fail: (error) => {
          console.error("图片上传网络失败:", error);
          // 上传失败时直接从列表中移除该项目
          const index = this.mediaList.findIndex(item => item === mediaItem);
          if (index > -1) {
            this.mediaList.splice(index, 1);
          }
          this.$emit("upload-error", error);
          uni.showToast({
            title: "图片上传失败",
            icon: "none",
          });
        },
        complete: () => {
          uni.hideLoading();
        },
      });
    },

    // 上传视频到服务器
    uploadVideoToServer(mediaItem) {
      mediaItem.uploading = true;

      uni.showLoading({
        title: "上传视频中...",
        mask: true
      });

      const uploadUrl = this.baseUrl + this.videoUploadUrl;

      uni.uploadFile({
        url: uploadUrl,
        filePath: mediaItem.tempPath,
        name: "file",
        header: {
          // 添加必要的请求头
          "parentId": "81556ac364d84cc2b77817841dab21d2",
          'ac': uni.getStorageSync('token3'),
          'pid': this.$store?.state?.userInfo?.userId || ''
        },
        success: (res) => {
          try {
            console.log("视频上传响应:", res);

            // 检查HTTP状态码
            if (res.statusCode !== 200) {
              throw new Error(`服务器错误: ${res.statusCode}`);
            }

            const data = JSON.parse(res.data);
            console.log("解析后的响应数据:", data);

            if (data.code === 2000 || data.code === 200 || data.success) {
              // 更新媒体项数据，使用服务器返回的视频URL和封面URL
              mediaItem.url =
                data.data?.videoUrl ||
                data.data?.imageUrl ||
                data.data?.url ||
                data.videoUrl ||
                data.imageUrl ||
                data.url ||
                mediaItem.url;
              mediaItem.poster =
                data.data?.coverUrl ||
                data.data?.thumbnailsUrl ||
                data.data?.posterUrl ||
                data.coverUrl ||
                data.thumbnailsUrl ||
                data.posterUrl ||
                mediaItem.poster;
              mediaItem.uploaded = true;
              mediaItem.uploading = false;

              console.log("视频上传成功，更新数据:", {
                originalUrl: mediaItem.tempPath,
                newUrl: mediaItem.url,
                originalPoster: res.thumbTempFilePath,
                newPoster: mediaItem.poster,
                serverData: data.data,
                fullResponse: data,
              });

              console.log("服务器数据结构分析:", {
                "data.data": data.data,
                "可用的封面字段": {
                  "data.data.coverUrl": data.data?.coverUrl,
                  "data.data.thumbnailsUrl": data.data?.thumbnailsUrl,
                  "data.data.posterUrl": data.data?.posterUrl,
                  "data.coverUrl": data.coverUrl,
                  "最终封面URL": mediaItem.poster
                }
              });

              this.$emit("upload-success", mediaItem);

              uni.showToast({
                title: "视频上传成功",
                icon: "success",
              });
            } else {
              throw new Error(data.message || `上传失败，错误代码: ${data.code}`);
            }
          } catch (error) {
            console.error("视频上传失败:", error);
            // 上传失败时直接从列表中移除该项目
            const index = this.mediaList.findIndex(item => item === mediaItem);
            if (index > -1) {
              this.mediaList.splice(index, 1);
            }
            this.$emit("upload-error", error);
            uni.showToast({
              title: error.message || "视频上传失败",
              icon: "none",
            });
          }
        },
        fail: (error) => {
          console.error("视频上传请求失败:", error);
          // 上传失败时直接从列表中移除该项目
          const index = this.mediaList.findIndex(item => item === mediaItem);
          if (index > -1) {
            this.mediaList.splice(index, 1);
          }
          this.$emit("upload-error", error);
          uni.showToast({
            title: "视频上传失败",
            icon: "none",
          });
        },
        complete: () => {
          uni.hideLoading();
        },
      });
    },

    // 旧的上传方法（保持兼容性）
    uploadFile(mediaItem) {
      if (mediaItem.type === "image") {
        this.uploadSingleImageToServer(mediaItem);
      } else if (mediaItem.type === "video") {
        this.uploadVideoToServer(mediaItem);
      }
    },

    // 获取所有媒体文件
    getMediaList() {
      return this.mediaList;
    },

    // 清空媒体列表
    clear() {
      this.mediaList = [];
    },

    // 处理图片加载错误
    handleImageError(item, index) {
      console.log("封面图片加载失败，使用备用方案:", item);
      // 标记封面图片加载失败，使用备用显示方案
      this.$set(this.mediaList[index], "posterError", true);
    },



    // 处理视频加载错误
    handleVideoError(item, index) {
      console.log("视频缩略图加载失败:", item);
      this.$set(this.mediaList[index], "useVideoElement", true);
    },

    // 视频加载完成
    onVideoLoaded(item, index) {
      console.log("视频缩略图加载成功:", item);
    },

    // 生成视频封面（App端使用）
    generateVideoPoster(mediaItem) {
      // #ifdef APP-PLUS
      console.log("App端生成视频封面");
      // 在App端，我们依赖video元素来显示第一帧
      mediaItem.useVideoElement = true;
      // #endif

      // #ifdef H5
      // H5端可以尝试使用canvas生成封面
      this.generateH5VideoPoster(mediaItem);
      // #endif
    },

    // H5端生成视频封面
    generateH5VideoPoster(mediaItem) {
      // 这里可以实现H5端的视频封面生成逻辑
      console.log("H5端视频封面生成");
    },
  },
};
</script>

<style scoped lang="scss">
.media-uploader {
  .media-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .media-item {
      position: relative;
      width: 200rpx;
      height: 200rpx;
      border-radius: 12rpx;
      overflow: hidden;

      .media-preview {
        width: 100%;
        height: 100%;
        position: relative;
        border-radius: 12rpx;
        overflow: hidden;

        .image-preview {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .video-preview {
        position: relative;
        background-color: #000;
        width: 100%;
        height: 100%;

        .video-poster {
          width: 100%;
          height: 100%;
          object-fit: cover;

          &.video-thumbnail {
            background-color: #f0f0f0;
          }
        }

        .video-placeholder {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;

          .video-icon {
            font-size: 48rpx;
            opacity: 0.8;
          }
        }

        .video-uploading-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.7);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          z-index: 10;
          border-radius: 12rpx;

          .uploading-spinner {
            width: 50rpx;
            height: 50rpx;
            border: 4rpx solid rgba(255, 255, 255, 0.3);
            border-top: 4rpx solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16rpx;
          }

          .uploading-text {
            font-size: 24rpx;
            color: #fff;
            font-weight: 500;
            text-shadow: 0 0 4rpx rgba(0, 0, 0, 0.5);
          }
        }

        .video-play-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 5;

          .video-play-btn {
            width: 64rpx;
            height: 64rpx;
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2rpx solid rgba(255, 255, 255, 0.9);
            box-shadow: 0 0 16rpx rgba(0, 0, 0, 0.3);

            .play-triangle {
              width: 0;
              height: 0;
              border-left: 20rpx solid #fff;
              border-top: 12rpx solid transparent;
              border-bottom: 12rpx solid transparent;
              margin-left: 4rpx;
            }
          }
        }
      }

      .image-uploading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
        border-radius: 12rpx;

        .uploading-spinner {
          width: 50rpx;
          height: 50rpx;
          border: 4rpx solid rgba(255, 255, 255, 0.3);
          border-top: 4rpx solid #fff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 16rpx;
        }

        .uploading-text {
          font-size: 24rpx;
          color: #fff;
          font-weight: 500;
          text-shadow: 0 0 4rpx rgba(0, 0, 0, 0.5);
        }
      }



      .delete-btn {
        position: absolute;
        top: 8rpx;
        right: 8rpx;
        width: 32rpx;
        height: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 15;
        filter: drop-shadow(0 0 4rpx rgba(0, 0, 0, 0.8));
      }
    }

    .add-btn {
      width: 200rpx;
      height: 200rpx;
      border: 2rpx solid #ddd;
      border-radius: 24rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #fafafa;

      .add-text {
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
      }
    }
  }
}

.action-sheet {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .action-header {
    text-align: center;
    padding: 32rpx;
    font-size: 32rpx;
    font-weight: bold;
    border-bottom: 1rpx solid #eee;
  }

  .action-list {
    .action-item {
      display: flex;
      align-items: center;
      padding: 32rpx;
      border-bottom: 1rpx solid #eee;

      text {
        margin-left: 24rpx;
        font-size: 30rpx;
      }

      &:active {
        background-color: #f5f5f5;
      }
    }
  }

  .action-cancel {
    text-align: center;
    padding: 32rpx;
    font-size: 30rpx;
    color: #999;
    border-top: 16rpx solid #f5f5f5;

    &:active {
      background-color: #f5f5f5;
    }
  }
}

// 视频预览弹窗样式
.video-preview-fullscreen {
  width: 100vw;
  height: 100vh;
  background-color: #000;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .video-preview-close {
    position: absolute;
    top: 60rpx;
    right: 32rpx;
    width: 64rpx;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    filter: drop-shadow(0 0 8rpx rgba(0, 0, 0, 0.8));
  }

  .video-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .debug-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40rpx;
      text-align: center;

      text {
        margin-bottom: 20rpx;
        word-break: break-all;
      }
    }
  }

  .video-error-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx;
    text-align: center;

    text {
      line-height: 1.5;
    }
  }

  .video-preview-player {
    width: 100%;
    height: 100%;
    min-height: 400rpx;
    background-color: #000;
  }
}

// 删除确认弹窗样式
.delete-confirm-modal {
  width: 560rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;

  .delete-confirm-title {
    text-align: center;
    padding: 48rpx 32rpx 24rpx;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }

  .delete-confirm-content {
    text-align: center;
    padding: 0 32rpx 48rpx;
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }

  .delete-confirm-actions {
    display: flex;
    border-top: 1rpx solid #eee;

    .delete-confirm-btn {
      flex: 1;
      text-align: center;
      padding: 32rpx;
      font-size: 32rpx;

      &.cancel {
        color: #666;
        border-right: 1rpx solid #eee;
      }

      &.confirm {
        color: #ff4757;
        font-weight: bold;
      }

      &:active {
        background-color: #f5f5f5;
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


</style>
