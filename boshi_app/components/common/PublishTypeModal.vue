<template>
  <view
    class="publish-modal"
    v-if="visible"
    @click="handleMaskClick"
    @touchmove.prevent
    @scroll.prevent
  >
    <view
      class="modal-content"
      @click.stop
      @touchmove.stop
    >
      <view class="modal-header">
        <text class="modal-title">选择发布类型</text>
        <view class="close-btn" @click="handleClose">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="publish-types">
        <view 
          class="type-item"
          v-for="(item, index) in publishTypes"
          :key="index"
          @click="handleTypeSelect(item)"
        >
          <view class="type-icon">
            <image :src="item.icon" mode="aspectFit"></image>
          </view>
          <view class="type-info">
            <text class="type-title">{{ item.title }}</text>
            <text class="type-desc">{{ item.description }}</text>
          </view>
          <view class="type-arrow">
            <text class="arrow-icon">></text>
          </view>
        </view>
      </view>
      
      <view class="modal-footer">
        <view class="cancel-btn" @click="handleClose">
          <text>取消</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import scrollLock from '@/utils/scrollLock.js';

export default {
  name: "PublishTypeModal",
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          scrollLock.lock();
        });
      } else {
        scrollLock.unlock();
      }
    }
  },
  data() {
    return {
      publishTypes: [
        {
          id: 'post',
          title: '发布帖子',
          description: '分享你的想法和经验',
          icon: '/static/publish/post_icon.png',
          path: '/pages/publish/post'
        },
        {
          id: 'question',
          title: '提问求助',
          description: '向达人提问获得帮助',
          icon: '/static/publish/question_icon.png',
          path: '/pages/publish/question'
        },
        {
          id: 'goods',
          title: '发布商品',
          description: '上架你的商品到商城',
          icon: '/static/publish/goods_icon.png',
          path: '/pages/publish/goods'
        },
        {
          id: 'activity',
          title: '发起活动',
          description: '组织线上线下活动',
          icon: '/static/publish/activity_icon.png',
          path: '/pages/publish/activity'
        }
      ]
    }
  },
  methods: {
    handleMaskClick(e) {
      // 确保只有点击遮罩层才关闭弹窗
      if (e.target === e.currentTarget) {
        this.handleClose();
      }
    },
    handleClose() {
      scrollLock.unlock(); // 关闭时恢复滚动
      this.$emit('close');
    },
    handleTypeSelect(type) {
      this.$emit('select', type);
      this.handleClose();

      // 跳转到对应的发布页面
      uni.navigateTo({
        url: type.path
      });
    }
  },

  beforeDestroy() {
    // 组件销毁时确保恢复滚动
    scrollLock.unlock();
  }
}
</script>

<style scoped lang="scss">
.publish-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;

  /* 防止背景滚动 */
  overflow: hidden;
  touch-action: none;

  /* 阻止事件穿透 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
  }
}

.modal-content {
  width: 100%;
  max-height: 80vh;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  animation: slideUp 0.3s ease-out;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  /* 确保弹窗内容可以滚动 */
  touch-action: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 32rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .modal-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
  
  .close-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f5f5f5;
    
    .close-icon {
      font-size: 40rpx;
      color: #999;
      line-height: 1;
    }
  }
}

.publish-types {
  padding: 20rpx 0;
  flex: 1;
  overflow-y: auto;

  .type-item {
    display: flex;
    align-items: center;
    padding: 32rpx;
    margin: 0 32rpx;
    border-radius: 16rpx;
    background: #fafafa;
    margin-bottom: 20rpx;
    transition: all 0.3s ease;
    
    &:active {
      transform: scale(0.98);
      background: #f0f0f0;
    }
    
    .type-icon {
      width: 80rpx;
      height: 80rpx;
      margin-right: 24rpx;
      
      image {
        width: 100%;
        height: 100%;
      }
    }
    
    .type-info {
      flex: 1;
      
      .type-title {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .type-desc {
        display: block;
        font-size: 26rpx;
        color: #666;
      }
    }
    
    .type-arrow {
      .arrow-icon {
        font-size: 32rpx;
        color: #ccc;
      }
    }
  }
}

.modal-footer {
  padding: 20rpx 32rpx 40rpx;
  
  .cancel-btn {
    width: 100%;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 44rpx;
    background: #f5f5f5;
    
    text {
      font-size: 32rpx;
      color: #666;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style>
