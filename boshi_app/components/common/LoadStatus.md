# LoadStatus 通用加载状态组件

一个通用的加载状态组件，支持加载中、无更多数据、空数据等多种状态显示。

## 功能特性

- 🔄 加载中状态（带旋转动画）
- 📄 无更多数据提示
- 📭 空数据提示
- 🎨 多主题支持（default、light、dark）
- 📝 自定义文本内容
- 🎯 居中显示，适配各种布局

## 基础用法

```vue
<template>
  <view>
    <!-- 数据列表 -->
    <view v-for="item in dataList" :key="item.id">
      {{ item.name }}
    </view>
    
    <!-- 加载状态 -->
    <LoadStatus 
      :loading="loading"
      :hasMore="hasMore"
      :total="dataList.length"
    />
  </view>
</template>

<script>
import LoadStatus from "@/components/common/LoadStatus.vue";

export default {
  components: { LoadStatus },
  data() {
    return {
      loading: false,
      hasMore: true,
      dataList: []
    }
  }
}
</script>
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| loading | Boolean | false | 是否正在加载 |
| hasMore | Boolean | true | 是否还有更多数据 |
| total | Number | 0 | 数据总数 |
| showStatus | Boolean | true | 是否显示状态 |
| loadingText | String | "加载中..." | 加载中文本 |
| noMoreText | String | "没有更多数据了" | 无更多数据文本 |
| emptyText | String | "暂无数据" | 空数据文本 |
| theme | String | "default" | 主题样式 |

## 主题样式

### default（默认主题）
```vue
<LoadStatus theme="default" />
```

### light（浅色主题）
```vue
<LoadStatus theme="light" />
```

### dark（深色主题）
```vue
<LoadStatus theme="dark" />
```

## 自定义文本

```vue
<LoadStatus 
  :loading="loading"
  :hasMore="hasMore"
  :total="goodsList.length"
  loadingText="正在加载商品..."
  noMoreText="所有商品已加载完毕"
  emptyText="暂无商品信息"
/>
```

## 分页加载示例

```vue
<template>
  <view>
    <view v-for="item in dataList" :key="item.id">
      {{ item.name }}
    </view>
    
    <LoadStatus 
      :loading="loading"
      :hasMore="hasMore"
      :total="dataList.length"
    />
  </view>
</template>

<script>
export default {
  data() {
    return {
      dataList: [],
      loading: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 10,
      totalCount: 0
    }
  },
  
  onReachBottom() {
    this.loadMore();
  },
  
  methods: {
    async loadMore() {
      if (this.loading || !this.hasMore) return;
      
      this.loading = true;
      
      try {
        const res = await this.fetchData({
          page: this.currentPage,
          size: this.pageSize
        });
        
        this.dataList = [...this.dataList, ...res.data];
        this.totalCount = res.total;
        this.hasMore = this.dataList.length < this.totalCount;
        this.currentPage++;
        
      } catch (error) {
        console.error('加载失败:', error);
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>
```

## 状态显示逻辑

1. **加载中**：`loading = true` 时显示
2. **无更多数据**：`loading = false` 且 `hasMore = false` 且 `total > 0` 时显示
3. **空数据**：`loading = false` 且 `total = 0` 时显示
4. **隐藏**：其他情况下隐藏

## 样式自定义

组件使用 scoped 样式，如需自定义样式，可以通过以下方式：

```vue
<LoadStatus class="custom-load-status" />

<style>
.custom-load-status {
  padding: 60rpx 0 !important;
}
</style>
```
