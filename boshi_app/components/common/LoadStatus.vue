<template>
  <view class="load-status" v-if="showStatus">
    <!-- 加载中状态 -->
    <view v-if="loading" class="status-item loading">
      <view class="loading-icon">
        <view class="spinner"></view>
      </view>
      <text class="status-text">{{ loadingText }}</text>
    </view>
    
    <!-- 没有更多数据状态 -->
    <view v-else-if="!hasMore && total > 0" class="status-item no-more">
      <text class="status-text">{{ noMoreText }}</text>
    </view>
    
    <!-- 空数据状态 -->
    <view v-else-if="total === 0 && !loading" class="status-item empty">
      <text class="status-text">{{ emptyText }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: "LoadStatus",
  props: {
    // 是否正在加载
    loading: {
      type: Boolean,
      default: false
    },
    // 是否还有更多数据
    hasMore: {
      type: Boolean,
      default: true
    },
    // 数据总数
    total: {
      type: Number,
      default: 0
    },
    // 是否显示状态（可用于控制显示时机）
    showStatus: {
      type: Boolean,
      default: true
    },
    // 自定义文本
    loadingText: {
      type: String,
      default: "加载中..."
    },
    noMoreText: {
      type: String,
      default: "没有更多数据了"
    },
    emptyText: {
      type: String,
      default: "暂无数据"
    },
    // 样式主题
    theme: {
      type: String,
      default: "default", // default, light, dark
      validator: value => ["default", "light", "dark"].includes(value)
    }
  }
};
</script>

<style scoped lang="scss">
.load-status {
  width: 100%;
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    &.loading {
      .loading-icon {
        margin-bottom: 20rpx;
        
        .spinner {
          width: 40rpx;
          height: 40rpx;
          border: 4rpx solid #f3f3f3;
          border-top: 4rpx solid #8147FF;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
      
      .status-text {
        color: #666;
        font-size: 28rpx;
      }
    }
    
    &.no-more {
      .status-text {
        color: #ccc;
        font-size: 26rpx;
      }
    }
    
    &.empty {
      .status-text {
        color: #999;
        font-size: 28rpx;
      }
    }
  }
}

// 主题样式
.load-status[data-theme="light"] {
  .status-item {
    &.loading .status-text {
      color: #999;
    }
    &.no-more .status-text {
      color: #ddd;
    }
    &.empty .status-text {
      color: #bbb;
    }
  }
}

.load-status[data-theme="dark"] {
  .status-item {
    &.loading .status-text {
      color: #fff;
    }
    &.no-more .status-text {
      color: #888;
    }
    &.empty .status-text {
      color: #aaa;
    }
  }
}

// 旋转动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
