<template>
  <view class="tab-list" :style="{ 'justify-content': justifyContent }">
    <view
      v-for="(item, index) in tabList"
      :key="index"
      class="tab-item"
      :class="{ active: currentTab === item.tab }"
      @click="handleTabClick(item, index)"
      :style="tabItemStyle"
    >
      <view class="tab-title">{{ item.title }}</view>
    </view>
  </view>
</template>

<script>
export default {
  name: "Tab",
  props: {
    // Tab 列表数据
    tabList: { type: Array, default: [] },
    // 默认选中的 tab
    defaultTab: { type: Number, default: 1 },
    // 正常状态文字颜色
    textColor: { type: String, default: "#878787" },
    // 选中状态文字颜色和下划线颜色
    activeColor: { type: String, default: "#8147FF" },
    // Tab 项的对齐方式
    justifyContent: { type: String, default: "start" },
    // 最小宽度
    minWidth: { type: String, default: "" },
  },
  data() {
    return {
      currentTab: this.defaultTab,
    };
  },
  computed: {
    tabItemStyle() {
      return {
        '--text-color': this.textColor,
        '--active-color': this.activeColor,
        '--min-width': this.minWidth,
        'min-width': this.minWidth, // 直接设置 min-width 确保兼容性
        'color': this.textColor // 设置默认文字颜色
      };
    }
  },
  methods: {
    handleTabClick(item, index) {
      this.currentTab = item.tab;
      this.$emit("tab-change", {
        tab: item.tab,
        index: index,
        item: item,
      });
    },
  },
  watch: {
    defaultTab: {
      handler(newVal) {
        this.currentTab = newVal;
      },
      immediate: true,
    },
  },
};
</script>

<style scoped lang="scss">
.tab-list {
  height: 88rpx;
  display: flex;
  align-items: center;
  gap: 64rpx;

  .tab-item {
    max-width: 200rpx;
    min-width: var(--min-width, 100rpx);
    text-align: center;
    position: relative;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &:active {
      transform: scale(0.98);
    }

    .tab-title {
      font-size: 28rpx;
      color: var(--text-color, #878787);
      font-weight: 400;
      transition:
        color 0.3s cubic-bezier(0.34, 1.56, 0.64, 1),
        font-size 0.3s cubic-bezier(0.34, 1.56, 0.64, 1),
        font-weight 0.3s cubic-bezier(0.34, 1.56, 0.64, 1),
        transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      transform: scale(1);
    }

    &::after {
      content: "";
      position: absolute;
      bottom: -8rpx;
      left: 50%;
      transform: translateX(-50%) scaleX(0);
      width: 48rpx;
      height: 4rpx;
      background: var(--active-color, #000);
      border-radius: 2rpx;
      transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
      transform-origin: center;
    }

    &.active {
      .tab-title {
        color: var(--active-color, #000);
        font-weight: 600;
        font-size: 32rpx;
        transform: scale(1.02);
      }

      &::after {
        transform: translateX(-50%) scaleX(1);
      }
    }

    &:hover:not(.active) {
      .tab-title {
        color: var(--active-color, #333);
        transform: scale(1.05);
        font-weight: 500;
      }
    }
  }
}
</style>
