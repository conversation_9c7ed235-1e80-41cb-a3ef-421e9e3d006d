<template>
  <view class="score-chart">
    <view class="chart-container">
      <!-- Canvas 绘制半环图表 -->
      <canvas
        class="dot-arc"
        canvas-id="scoreChart"
        id="scoreChart"
        :width="canvasSize"
        :height="canvasSize"
        :style="{ width: canvasSize + 'px', height: canvasSize + 'px' }"
        @touchstart="handleTouch"
      ></canvas>

      <!-- 中心内容 -->
      <view class="center-content">
        <view class="score-number">{{ score }}</view>
        <view class="score-label">{{ label }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "ScoreCharts",
  props: {
    score: { type: [Number, String], default: 60 },
    maxScore: { type: Number, default: 100 },
    label: { type: String, default: "综合分数" },
    activeColor: { type: String, default: "#8F73FC" },
    inactiveColor: { type: String, default: "#E2E7F5" },
    segments: { type: Number, default: 21 }, // 点数量
    radius: { type: Number, default: 45 },
    dotSize: { type: Number, default: 8 },
    dotWidth: { type: Number, default: 5 },
    dotHeight: { type: Number, default: 8 },
    // 起止角度：默认从 8 点钟(150°) 到 4 点钟(390°)
    startAngle: { type: Number, default: 150 },
    endAngle: { type: Number, default: 390 },
    canvasSize: { type: Number, default: 100 },
  },
  data() {
    return {
      ctx: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initCanvas();
    });
  },
  watch: {
    score() {
      this.drawChart();
    },
  },
  methods: {
    initCanvas() {
      this.ctx = uni.createCanvasContext("scoreChart", this);
      this.drawChart();
    },
    drawChart() {
      if (!this.ctx) return;

      const ctx = this.ctx;
      const size = this.canvasSize;
      const cx = size / 2;
      const cy = size / 2; // 圆心在画布中心
      const r = this.radius;

      // 清空画布
      ctx.clearRect(0, 0, size, size);

      // 调试：绘制圆心标记
      // ctx.beginPath();
      // ctx.arc(cx, cy, 5, 0, 2 * Math.PI);
      // ctx.setFillStyle("#FF0000");
      // ctx.fill();

      // 调试：绘制画布边界
      // ctx.beginPath();
      // ctx.rect(0, 0, size, size);
      // ctx.setStrokeStyle("#00FF00");
      // ctx.setLineWidth(2);
      // ctx.stroke();

      // 计算激活的点数
      const activeCount = Math.round(
        (Number(this.score) / this.maxScore) * this.segments,
      );

      // 绘制半圆弧上的圆点
      for (let i = 0; i < this.segments; i++) {
        // 使用props中的角度设置
        const startAngleRad = (this.startAngle * Math.PI) / 180; // 转换为弧度
        const endAngleRad = (this.endAngle * Math.PI) / 180; // 转换为弧度
        const angle =
          startAngleRad +
          (i / (this.segments - 1)) * (endAngleRad - startAngleRad);
        const x = cx + r * Math.cos(angle);
        const y = cy + r * Math.sin(angle);

        const active = i < activeCount;

        // 绘制圆角矩形而不是圆形
        const rectWidth = this.dotWidth;
        const rectHeight = this.dotHeight;
        const cornerRadius = Math.min(rectWidth, rectHeight) / 2; // 圆角半径

        // 保存当前画布状态
        ctx.save();

        // 移动到点的位置
        ctx.translate(x, y);

        // 旋转使短边朝向圆心（角度 + 90度，因为矩形默认是水平的）
        ctx.rotate(angle + Math.PI / 2);

        ctx.beginPath();
        // 绘制圆角矩形（以原点为中心）
        const rectX = -rectWidth / 2;
        const rectY = -rectHeight / 2;

        ctx.moveTo(rectX + cornerRadius, rectY);
        ctx.lineTo(rectX + rectWidth - cornerRadius, rectY);
        ctx.quadraticCurveTo(
          rectX + rectWidth,
          rectY,
          rectX + rectWidth,
          rectY + cornerRadius,
        );
        ctx.lineTo(rectX + rectWidth, rectY + rectHeight - cornerRadius);
        ctx.quadraticCurveTo(
          rectX + rectWidth,
          rectY + rectHeight,
          rectX + rectWidth - cornerRadius,
          rectY + rectHeight,
        );
        ctx.lineTo(rectX + cornerRadius, rectY + rectHeight);
        ctx.quadraticCurveTo(
          rectX,
          rectY + rectHeight,
          rectX,
          rectY + rectHeight - cornerRadius,
        );
        ctx.lineTo(rectX, rectY + cornerRadius);
        ctx.quadraticCurveTo(rectX, rectY, rectX + cornerRadius, rectY);
        ctx.closePath();

        if (active) {
          // 激活状态：紫色渐变，从深紫到浅紫
          const progress = i / Math.max(1, this.segments - 1);
          // 使用更丰富的渐变色彩
          if (progress < 0.3) {
            ctx.setFillStyle("#8F73FC"); // 深紫色
          } else if (progress < 0.6) {
            ctx.setFillStyle("#A085FD"); // 中紫色
          } else {
            ctx.setFillStyle("#B197FE"); // 浅紫色
          }
        } else {
          // 未激活状态：浅灰色
          ctx.setFillStyle("#E8EBF0");
        }

        ctx.fill();

        // 恢复画布状态
        ctx.restore();
      }

      // 不绘制底部分割线，保持简洁

      // uni-app Canvas Context 必须调用 draw 方法
      ctx.draw();
    },
    handleTouch() {
      // 处理触摸事件（可选）
    },
  },
};
</script>

<style scoped>
.score-chart {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-container {
  position: relative;
}

.dot-arc {
  width: 200rpx;
  height: 200rpx;
  display: block;
}

.center-content {
  position: absolute;
  left: 50%;
  top: 55%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.score-number {
  display: block;
  font-family: DINCond-Black;
  font-size: 80rpx;
  font-weight: 800;
  color: #6345a3;
  margin-top: 20rpx;
  line-height: 1;
}

.score-label {
  display: block;
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #666666;
}
</style>
